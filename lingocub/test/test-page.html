<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LingoCub 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
        }
        
        .test-section h3 {
            color: #6366f1;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .test-button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 10px 10px 0;
            transition: background 0.3s;
        }
        
        .test-button:hover {
            background: #4f46e5;
        }
        
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
            display: none;
        }
        
        .result.success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .result.error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .result.info {
            background: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        
        .youtube-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .youtube-link {
            display: block;
            padding: 15px;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            text-decoration: none;
            color: #374151;
            transition: all 0.3s;
        }
        
        .youtube-link:hover {
            border-color: #6366f1;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
        }
        
        .youtube-link strong {
            color: #6366f1;
            display: block;
            margin-bottom: 5px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.success { background: #10b981; }
        .status-indicator.error { background: #ef4444; }
        .status-indicator.warning { background: #f59e0b; }
        .status-indicator.info { background: #3b82f6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 LingoCub 测试页面</h1>
            <p>验证扩展功能和诊断问题</p>
        </div>
        
        <div class="content">
            <!-- Extension Status -->
            <div class="test-section">
                <h3>🔧 扩展状态检查</h3>
                <p>检查LingoCub扩展是否正确加载和配置。</p>
                <button class="test-button" onclick="checkExtensionStatus()">检查扩展状态</button>
                <div id="extensionResult" class="result"></div>
            </div>
            
            <!-- API Key Test -->
            <div class="test-section">
                <h3>🔑 API密钥测试</h3>
                <p>验证您的ElevenLabs API密钥是否有效。</p>
                <button class="test-button" onclick="testApiKey()">测试API密钥</button>
                <div id="apiResult" class="result"></div>
            </div>
            
            <!-- Network Test -->
            <div class="test-section">
                <h3>🌐 网络连接测试</h3>
                <p>检查与ElevenLabs API和代理服务器的连接。</p>
                <button class="test-button" onclick="testNetwork()">测试网络连接</button>
                <div id="networkResult" class="result"></div>
            </div>
            
            <!-- Full Diagnostic -->
            <div class="test-section">
                <h3>🔍 完整诊断</h3>
                <p>运行完整的系统诊断，检查所有组件。</p>
                <button class="test-button" onclick="runFullDiagnostic()">运行完整诊断</button>
                <div id="diagnosticResult" class="result"></div>
            </div>
            
            <!-- Test Videos -->
            <div class="test-section">
                <h3>🎥 测试视频</h3>
                <p>使用这些短视频测试配音功能：</p>
                <div class="youtube-links">
                    <a href="https://www.youtube.com/watch?v=dQw4w9WgXcQ" target="_blank" class="youtube-link">
                        <strong>🎵 经典音乐视频</strong>
                        <span>英语，约3分钟</span>
                    </a>
                    <a href="https://www.youtube.com/watch?v=9bZkp7q19f0" target="_blank" class="youtube-link">
                        <strong>🐱 可爱动物视频</strong>
                        <span>英语，约2分钟</span>
                    </a>
                    <a href="https://www.youtube.com/watch?v=jNQXAC9IVRw" target="_blank" class="youtube-link">
                        <strong>🎤 TED演讲</strong>
                        <span>英语，约18分钟</span>
                    </a>
                    <a href="https://www.youtube.com/watch?v=BaW_jenozKc" target="_blank" class="youtube-link">
                        <strong>🎬 短片</strong>
                        <span>英语，约4分钟</span>
                    </a>
                </div>
            </div>
            
            <!-- Instructions -->
            <div class="test-section">
                <h3>📋 测试说明</h3>
                <ol>
                    <li>首先运行"检查扩展状态"确保扩展正确加载</li>
                    <li>配置您的ElevenLabs API密钥</li>
                    <li>运行"测试API密钥"验证密钥有效性</li>
                    <li>测试网络连接确保可以访问必要的服务</li>
                    <li>选择一个测试视频进行实际配音测试</li>
                    <li>如果遇到问题，运行"完整诊断"获取详细信息</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // Check if Chrome extension APIs are available
        function isExtensionContext() {
            return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
        }
        
        // Show result in specified element
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
        
        // Check extension status
        async function checkExtensionStatus() {
            if (!isExtensionContext()) {
                showResult('extensionResult', '❌ 扩展环境未检测到\n请确保：\n1. LingoCub扩展已安装\n2. 在扩展弹窗中打开此页面\n3. 或在YouTube页面打开此页面', 'error');
                return;
            }
            
            try {
                const manifest = chrome.runtime.getManifest();
                const data = await new Promise(resolve => chrome.storage.local.get(null, resolve));
                
                let status = `✅ 扩展状态正常\n`;
                status += `📦 名称: ${manifest.name}\n`;
                status += `🔢 版本: ${manifest.version}\n`;
                status += `🔑 API密钥: ${data.apiKey ? '已配置' : '未配置'}\n`;
                status += `⚙️ 设置: ${data.sourceLanguage && data.targetLanguage ? '已配置' : '未配置'}`;
                
                showResult('extensionResult', status, 'success');
            } catch (error) {
                showResult('extensionResult', `❌ 检查失败: ${error.message}`, 'error');
            }
        }
        
        // Test API key
        async function testApiKey() {
            if (!isExtensionContext()) {
                showResult('apiResult', '❌ 需要扩展环境才能测试API密钥', 'error');
                return;
            }
            
            try {
                const data = await new Promise(resolve => chrome.storage.local.get(['apiKey'], resolve));
                
                if (!data.apiKey) {
                    showResult('apiResult', '❌ 未找到API密钥\n请在扩展弹窗中配置您的ElevenLabs API密钥', 'error');
                    return;
                }
                
                showResult('apiResult', '🔄 正在测试API密钥...', 'info');
                
                const response = await fetch('https://api.elevenlabs.io/v1/user', {
                    headers: { 'xi-api-key': data.apiKey.trim() }
                });
                
                if (response.ok) {
                    const userData = await response.json();
                    const tier = userData.subscription?.tier || '未知';
                    const charCount = userData.subscription?.character_count || 0;
                    const charLimit = userData.subscription?.character_limit || 0;
                    const remaining = charLimit - charCount;
                    
                    let result = `✅ API密钥有效\n`;
                    result += `👤 订阅类型: ${tier}\n`;
                    result += `📊 字符配额: ${remaining}/${charLimit} 剩余`;
                    
                    showResult('apiResult', result, 'success');
                } else {
                    showResult('apiResult', `❌ API密钥无效\nHTTP ${response.status}: 请检查您的密钥`, 'error');
                }
            } catch (error) {
                showResult('apiResult', `❌ 测试失败: ${error.message}`, 'error');
            }
        }
        
        // Test network connectivity
        async function testNetwork() {
            showResult('networkResult', '🔄 正在测试网络连接...', 'info');
            
            const tests = [
                { name: 'ElevenLabs API', url: 'https://api.elevenlabs.io' },
                { name: '代理服务器', url: 'https://lingocub.vercel.app' }
            ];
            
            let results = '🌐 网络连接测试结果:\n\n';
            let allPassed = true;
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url, { 
                        method: 'HEAD', 
                        mode: 'no-cors',
                        signal: AbortSignal.timeout(5000)
                    });
                    results += `✅ ${test.name}: 连接正常\n`;
                } catch (error) {
                    results += `❌ ${test.name}: 连接失败 (${error.message})\n`;
                    allPassed = false;
                }
            }
            
            showResult('networkResult', results, allPassed ? 'success' : 'error');
        }
        
        // Run full diagnostic
        async function runFullDiagnostic() {
            showResult('diagnosticResult', '🔄 正在运行完整诊断...', 'info');
            
            // Load and run the diagnostic script
            try {
                const response = await fetch('/debug/quick-diagnostic.js');
                const script = await response.text();
                
                // Capture console output
                const originalLog = console.log;
                let output = '';
                console.log = (...args) => {
                    output += args.join(' ') + '\n';
                    originalLog(...args);
                };
                
                // Run the diagnostic
                eval(script);
                
                // Restore console.log
                setTimeout(() => {
                    console.log = originalLog;
                    showResult('diagnosticResult', output || '诊断完成，请查看浏览器控制台获取详细信息', 'info');
                }, 2000);
                
            } catch (error) {
                showResult('diagnosticResult', `❌ 诊断失败: ${error.message}\n\n请手动在控制台运行诊断脚本`, 'error');
            }
        }
        
        // Auto-check extension status on page load
        window.addEventListener('load', () => {
            setTimeout(checkExtensionStatus, 1000);
        });
    </script>
</body>
</html>
