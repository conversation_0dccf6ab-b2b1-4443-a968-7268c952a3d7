// Test script for LingoCub Chrome Extension
// This script helps verify that the extension is working correctly

class LingoCubTester {
    constructor() {
        this.testResults = [];
        this.apiKey = null;
    }

    // Initialize test environment
    async init() {
        console.log('🧪 LingoCub Extension Tester Initialized');
        console.log('📋 Running comprehensive tests...\n');
        
        await this.runAllTests();
        this.displayResults();
    }

    // Run all test suites
    async runAllTests() {
        await this.testExtensionLoaded();
        await this.testManifestValidation();
        await this.testStorageAccess();
        await this.testUIElements();
        await this.testAPIKeyValidation();
        await this.testLanguageSelection();
        await this.testYouTubeIntegration();
        await this.testAudioControls();
    }

    // Test 1: Check if extension is loaded
    async testExtensionLoaded() {
        const testName = 'Extension Loading';
        try {
            const extensionId = chrome.runtime.id;
            if (extensionId) {
                this.addResult(testName, true, `Extension loaded with ID: ${extensionId}`);
            } else {
                this.addResult(testName, false, 'Extension ID not found');
            }
        } catch (error) {
            this.addResult(testName, false, `Error: ${error.message}`);
        }
    }

    // Test 2: Validate manifest configuration
    async testManifestValidation() {
        const testName = 'Manifest Validation';
        try {
            const manifest = chrome.runtime.getManifest();
            
            // Check required fields
            const requiredFields = ['name', 'version', 'manifest_version', 'permissions'];
            const missingFields = requiredFields.filter(field => !manifest[field]);
            
            if (missingFields.length === 0) {
                this.addResult(testName, true, `Manifest v${manifest.manifest_version} valid`);
            } else {
                this.addResult(testName, false, `Missing fields: ${missingFields.join(', ')}`);
            }
        } catch (error) {
            this.addResult(testName, false, `Error: ${error.message}`);
        }
    }

    // Test 3: Test storage access
    async testStorageAccess() {
        const testName = 'Storage Access';
        try {
            // Test write
            await chrome.storage.local.set({ testKey: 'testValue' });
            
            // Test read
            const result = await chrome.storage.local.get('testKey');
            
            if (result.testKey === 'testValue') {
                this.addResult(testName, true, 'Storage read/write successful');
                // Cleanup
                await chrome.storage.local.remove('testKey');
            } else {
                this.addResult(testName, false, 'Storage read/write failed');
            }
        } catch (error) {
            this.addResult(testName, false, `Error: ${error.message}`);
        }
    }

    // Test 4: Test UI elements
    async testUIElements() {
        const testName = 'UI Elements';
        try {
            // Check if popup elements exist
            const requiredElements = [
                'apiKey',
                'sourceLanguage', 
                'targetLanguage',
                'numSpeakers',
                'audioQuality',
                'startDubbing',
                'dubbedAudioToggle'
            ];

            const missingElements = requiredElements.filter(id => !document.getElementById(id));
            
            if (missingElements.length === 0) {
                this.addResult(testName, true, 'All UI elements present');
            } else {
                this.addResult(testName, false, `Missing elements: ${missingElements.join(', ')}`);
            }
        } catch (error) {
            this.addResult(testName, false, `Error: ${error.message}`);
        }
    }

    // Test 5: Test API key validation
    async testAPIKeyValidation() {
        const testName = 'API Key Validation';
        try {
            // Test with invalid key
            const isValidInvalid = await this.validateApiKey('invalid_key');
            
            if (!isValidInvalid) {
                this.addResult(testName, true, 'Invalid API key correctly rejected');
            } else {
                this.addResult(testName, false, 'Invalid API key incorrectly accepted');
            }
        } catch (error) {
            this.addResult(testName, false, `Error: ${error.message}`);
        }
    }

    // Test 6: Test language selection
    async testLanguageSelection() {
        const testName = 'Language Selection';
        try {
            const sourceSelect = document.getElementById('sourceLanguage');
            const targetSelect = document.getElementById('targetLanguage');
            
            if (sourceSelect && targetSelect) {
                const sourceOptions = sourceSelect.options.length;
                const targetOptions = targetSelect.options.length;
                
                if (sourceOptions > 20 && targetOptions > 20) {
                    this.addResult(testName, true, `${sourceOptions} source, ${targetOptions} target languages available`);
                } else {
                    this.addResult(testName, false, 'Insufficient language options');
                }
            } else {
                this.addResult(testName, false, 'Language selection elements not found');
            }
        } catch (error) {
            this.addResult(testName, false, `Error: ${error.message}`);
        }
    }

    // Test 7: Test YouTube integration
    async testYouTubeIntegration() {
        const testName = 'YouTube Integration';
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const currentTab = tabs[0];
            
            if (currentTab && currentTab.url && currentTab.url.includes('youtube.com')) {
                this.addResult(testName, true, 'YouTube page detected');
            } else {
                this.addResult(testName, false, 'Not on YouTube page (expected for testing)');
            }
        } catch (error) {
            this.addResult(testName, false, `Error: ${error.message}`);
        }
    }

    // Test 8: Test audio controls
    async testAudioControls() {
        const testName = 'Audio Controls';
        try {
            const toggle = document.getElementById('dubbedAudioToggle');
            const startBtn = document.getElementById('startDubbing');
            
            if (toggle && startBtn) {
                // Test toggle functionality
                const initialState = toggle.checked;
                toggle.click();
                const newState = toggle.checked;
                
                if (initialState !== newState) {
                    this.addResult(testName, true, 'Audio controls functional');
                } else {
                    this.addResult(testName, false, 'Audio toggle not working');
                }
            } else {
                this.addResult(testName, false, 'Audio control elements not found');
            }
        } catch (error) {
            this.addResult(testName, false, `Error: ${error.message}`);
        }
    }

    // Helper method to validate API key
    async validateApiKey(apiKey) {
        try {
            const response = await fetch("https://api.elevenlabs.io/v1/user", {
                headers: { "xi-api-key": apiKey },
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    // Add test result
    addResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message
        });
    }

    // Display test results
    displayResults() {
        console.log('\n📊 Test Results Summary:');
        console.log('========================\n');
        
        let passedCount = 0;
        let totalCount = this.testResults.length;
        
        this.testResults.forEach(result => {
            const status = result.passed ? '✅ PASS' : '❌ FAIL';
            console.log(`${status} ${result.name}: ${result.message}`);
            if (result.passed) passedCount++;
        });
        
        console.log('\n========================');
        console.log(`📈 Overall: ${passedCount}/${totalCount} tests passed`);
        
        if (passedCount === totalCount) {
            console.log('🎉 All tests passed! Extension is ready to use.');
        } else {
            console.log('⚠️  Some tests failed. Please check the issues above.');
        }
        
        return {
            passed: passedCount,
            total: totalCount,
            success: passedCount === totalCount
        };
    }

    // Performance test
    async testPerformance() {
        console.log('\n⚡ Running Performance Tests...');
        
        const startTime = performance.now();
        
        // Test popup load time
        const popupLoadStart = performance.now();
        // Simulate popup operations
        await new Promise(resolve => setTimeout(resolve, 100));
        const popupLoadTime = performance.now() - popupLoadStart;
        
        // Test storage operations
        const storageStart = performance.now();
        await chrome.storage.local.set({ perfTest: Date.now() });
        await chrome.storage.local.get('perfTest');
        await chrome.storage.local.remove('perfTest');
        const storageTime = performance.now() - storageStart;
        
        const totalTime = performance.now() - startTime;
        
        console.log(`📊 Performance Results:`);
        console.log(`   Popup Load: ${popupLoadTime.toFixed(2)}ms`);
        console.log(`   Storage Ops: ${storageTime.toFixed(2)}ms`);
        console.log(`   Total Time: ${totalTime.toFixed(2)}ms`);
    }
}

// Auto-run tests when script is loaded
if (typeof chrome !== 'undefined' && chrome.runtime) {
    const tester = new LingoCubTester();
    tester.init().then(() => {
        return tester.testPerformance();
    });
} else {
    console.log('❌ Chrome extension environment not detected');
    console.log('💡 Please run this script in the extension popup or background context');
}

// Export for manual testing
window.LingoCubTester = LingoCubTester;
