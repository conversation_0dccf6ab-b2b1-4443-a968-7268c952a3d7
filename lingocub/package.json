{"name": "lingocub", "version": "2.0.0", "description": "Real-time YouTube video dubbing Chrome extension with AI-powered voice translation", "main": "api/server.js", "author": "LingoCub Team", "license": "MIT", "homepage": "https://github.com/lingocub/extension", "repository": {"type": "git", "url": "https://github.com/lingocub/extension.git"}, "bugs": {"url": "https://github.com/lingocub/extension/issues"}, "keywords": ["youtube", "dubbing", "translation", "chrome-extension", "ai", "voice", "multilingual", "elevenlabs"], "scripts": {"start": "node api/server.js", "dev": "nodemon api/server.js", "test": "node test/test-extension.js", "build": "npm run build:extension && npm run build:api", "build:extension": "echo 'Building Chrome extension...' && npm run validate:manifest", "build:api": "echo 'Building API server...' && npm run test:api", "validate:manifest": "node scripts/validate-manifest.js", "test:api": "node test/test-api.js", "lint": "eslint extension/ api/ --ext .js", "lint:fix": "eslint extension/ api/ --ext .js --fix", "package": "npm run build && node scripts/package-extension.js", "deploy": "npm run build && npm run deploy:api", "deploy:api": "vercel --prod", "clean": "rm -rf dist/ *.zip", "docs": "jsdoc -c jsdoc.conf.json", "version:patch": "npm version patch && git push && git push --tags", "version:minor": "npm version minor && git push && git push --tags", "version:major": "npm version major && git push && git push --tags"}, "dependencies": {"cors": "^2.8.5", "node-fetch": "^2.7.0"}, "devDependencies": {"eslint": "^8.0.0", "nodemon": "^3.0.0", "jsdoc": "^4.0.0", "archiver": "^6.0.0"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["Chrome >= 88"]}