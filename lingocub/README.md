# 🎬 LingoCub - YouTube Video Dubbing Extension

LingoCub是一个强大的Chrome扩展程序，可以为YouTube视频提供实时多语言配音。使用ElevenLabs的AI语音技术，将视频内容翻译成您喜欢的语言，并提供高质量的配音体验。

## ✨ 主要功能

### 🌍 多语言支持
- **25+种语言**：支持英语、中文、西班牙语、法语、德语、日语、韩语等
- **自动检测**：智能识别源语言
- **实时翻译**：即时将视频内容翻译成目标语言

### 🎵 高质量音频
- **AI配音**：使用ElevenLabs先进的AI语音合成技术
- **多种音质**：标准、高质量、高级三种音质选项
- **说话人识别**：支持1-9个说话人的智能识别
- **音频同步**：精确的音频与视频同步

### 🎛️ 智能控制
- **一键切换**：在原始音频和配音之间无缝切换
- **实时状态**：显示配音进度和状态
- **快捷键支持**：Ctrl+Shift+D切换音频，Ctrl+Shift+R重启配音
- **自动缓存**：智能缓存机制提升性能

### 🎨 现代化界面
- **直观设计**：清晰易用的用户界面
- **实时反馈**：状态指示器和进度条
- **响应式布局**：适配不同屏幕尺寸
- **无障碍支持**：支持键盘导航和高对比度模式
### Installation
1. #### Clone the Repository:

```git clone https://github.com/chuckmilton/lingocub.git```

2. #### Load the Extension:

- Open Chrome and navigate to `chrome://extensions/`.
- Enable **Developer mode** in the top-right corner.
- Click on **Load unpacked** and select the cloned `lingocub` directory.

3. #### Setup API Key:

- Obtain an API key from [ElevenLabs](https://elevenlabs.io/api) for access to dubbing services.
- Enter the API key in the LingoCub extension popup.

### Usage
1. **Open YouTube** and navigate to a video.
2. Click on the **LingoCub extension icon** to open the settings popup.
3. **Enter API Key** (if not saved) and configure your settings:
- **Source Language**: Select the language of the original video (or set to auto-detect).
- **Target Language**: Choose the language for dubbing.
- **Number of Speakers**: Select the number of speakers (or set to auto-detect).
4. Click **Start Dubbing**.
5. **Dubbing Status** will display in the popup, and dubbed audio will play along with the video, with the original audio muted.

## 🔧 故障排除

### 常见问题快速解决

#### 🔑 API密钥问题
**错误信息**: "Dubbing request failed: Please check your API key"

**解决方案**:
1. 点击扩展中的"🧪 Test Key"按钮验证密钥
2. 从[ElevenLabs](https://elevenlabs.io/app/speech-synthesis)重新获取API密钥
3. 检查账户配额是否充足
4. 确保API密钥完整且无多余空格

#### 🎵 音频问题
- **音频不同步**: 刷新YouTube页面重新开始
- **无声音**: 检查音频切换开关状态
- **音质差**: 选择"高质量"设置
- **播放失败**: 确保YouTube视频可正常播放

#### 🌐 网络问题
- **连接超时**: 检查网络连接，尝试禁用VPN
- **加载失败**: 清除浏览器缓存，重新加载扩展
- **CORS错误**: 确保代理服务器正常运行

### 快速诊断工具

在浏览器控制台运行以下代码进行自动诊断：
```javascript
// 复制并粘贴到控制台运行
fetch('https://raw.githubusercontent.com/lingocub/extension/main/debug/quick-diagnostic.js').then(r=>r.text()).then(eval);
```

### 获取帮助

- 📖 **详细指南**: [故障排除文档](TROUBLESHOOTING.md)
- 🐛 **报告问题**: [GitHub Issues](https://github.com/lingocub/extension/issues)
- 💬 **讨论交流**: [GitHub Discussions](https://github.com/lingocub/extension/discussions)

### Disclaimer
#### Disclaimer for LingoCub

#### Content Usage
LingoCub is an unofficial tool designed to enhance YouTube video experiences by dubbing videos in real-time using the ElevenLabs API. LingoCub does not own or control YouTube or its content. Users are responsible for ensuring they have the right to alter or overlay audio on any video they view or use with this tool.

#### Third-Party API Integration
This app relies on the ElevenLabs API for audio generation, and all dubbed audio is generated based on information provided by ElevenLabs. Users are responsible for reviewing ElevenLabs’ privacy policy and terms of service, as data used for dubbing is managed by ElevenLabs and may be subject to its data handling policies.

#### Data Collection
LingoCub stores your API key locally and does not transmit it to third parties. Any data related to video URLs, language preferences, and dubbing requests is used solely for in-app functionality and is not stored or retained by LingoCub after usage.

#### Limitations and Errors
LingoCub’s performance may vary depending on YouTube’s restrictions, API limitations, or connection issues. We do not guarantee continuous functionality due to potential API updates, server limitations, or other service changes by YouTube or ElevenLabs.

#### Privacy and Security
By using this extension, users acknowledge and accept any associated risks, including potential disruptions in YouTube or ElevenLabs functionality. LingoCub is designed with user privacy in mind but cannot guarantee the privacy practices of third-party services or external links accessed through YouTube.

#### Non-Endorsement
This application is not endorsed or affiliated with YouTube, Google, or ElevenLabs. All trademarks and copyrights belong to their respective owners.

