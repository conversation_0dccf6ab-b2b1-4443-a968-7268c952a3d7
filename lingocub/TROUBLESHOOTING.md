# 🔧 LingoCub 故障排除指南

本指南帮助您解决使用LingoCub时可能遇到的常见问题。

## 🔑 API密钥相关问题

### 问题：显示"Dubbing request failed: Please check your API key and try again"

**可能原因和解决方案：**

#### 1. API密钥无效或错误
- **检查步骤：**
  - 确保从正确位置复制API密钥：[ElevenLabs API页面](https://elevenlabs.io/app/speech-synthesis)
  - 检查是否复制了完整的密钥（没有遗漏字符）
  - 确保没有多余的空格或换行符

- **解决方案：**
  - 重新登录ElevenLabs账户
  - 生成新的API密钥
  - 在扩展中使用"🧪 Test Key"按钮验证密钥

#### 2. API配额不足
- **检查步骤：**
  - 登录ElevenLabs查看剩余字符数
  - 检查当月使用情况

- **解决方案：**
  - 升级ElevenLabs订阅计划
  - 等待下月配额重置
  - 使用较短的视频进行测试

#### 3. 账户状态问题
- **检查步骤：**
  - 确认ElevenLabs账户状态正常
  - 检查是否有未付款项

- **解决方案：**
  - 联系ElevenLabs客服
  - 更新付款信息
  - 重新激活账户

#### 4. 网络连接问题
- **检查步骤：**
  - 测试网络连接
  - 检查防火墙设置
  - 尝试禁用VPN

- **解决方案：**
  - 切换网络连接
  - 配置防火墙允许访问elevenlabs.io
  - 暂时禁用VPN或代理

## 🎵 音频相关问题

### 问题：配音音频不播放

**解决方案：**
1. 检查浏览器音频权限
2. 确保YouTube视频可以正常播放
3. 尝试刷新页面重新开始
4. 检查音频切换开关状态

### 问题：音频与视频不同步

**解决方案：**
1. 刷新YouTube页面
2. 重新启动配音
3. 检查网络连接稳定性
4. 尝试降低音频质量设置

### 问题：音频质量差

**解决方案：**
1. 选择"高质量"或"高级"音质设置
2. 确保源视频音频清晰
3. 检查说话人数量设置是否正确
4. 尝试不同的目标语言

## 🌐 网络相关问题

### 问题：请求超时

**解决方案：**
1. 检查网络连接稳定性
2. 尝试使用有线连接
3. 关闭其他占用带宽的应用
4. 稍后重试

### 问题：CORS错误

**解决方案：**
1. 确保使用最新版本的扩展
2. 检查代理服务器状态
3. 尝试禁用其他扩展
4. 重新安装扩展

## 🎬 YouTube集成问题

### 问题：扩展在YouTube页面不工作

**解决方案：**
1. 确保在YouTube视频页面（包含/watch?v=）
2. 刷新页面重新加载扩展
3. 检查扩展权限设置
4. 尝试在无痕模式下使用

### 问题：视频检测失败

**解决方案：**
1. 确保视频完全加载
2. 尝试暂停后重新播放视频
3. 检查视频是否有地区限制
4. 尝试其他YouTube视频

## 🔧 扩展相关问题

### 问题：扩展无法加载

**解决方案：**
1. 检查Chrome版本（需要88+）
2. 确保启用了开发者模式
3. 重新加载扩展程序
4. 检查manifest.json文件完整性

### 问题：弹窗界面显示异常

**解决方案：**
1. 清除浏览器缓存
2. 禁用冲突的扩展
3. 重新安装扩展
4. 检查浏览器控制台错误

## 📊 性能问题

### 问题：处理速度慢

**解决方案：**
1. 选择较低的音频质量
2. 使用较短的视频进行测试
3. 关闭不必要的浏览器标签
4. 检查系统资源使用情况

### 问题：内存使用过高

**解决方案：**
1. 定期刷新YouTube页面
2. 清除扩展缓存
3. 重启浏览器
4. 检查其他扩展的内存使用

## 🛠️ 高级故障排除

### 启用调试模式

1. 打开Chrome开发者工具（F12）
2. 切换到Console标签
3. 查看错误信息
4. 将错误信息提供给技术支持

### 重置扩展设置

1. 右键点击扩展图标
2. 选择"选项"或"管理扩展"
3. 清除所有存储数据
4. 重新配置设置

### 收集诊断信息

运行以下代码在控制台中：
```javascript
// 在扩展弹窗中运行
chrome.storage.local.get(null, (data) => {
    console.log('Extension data:', data);
});
```

## 📞 获取帮助

如果以上解决方案都无法解决您的问题：

### 1. 检查已知问题
- 查看GitHub Issues页面
- 搜索相似问题的解决方案

### 2. 提交问题报告
包含以下信息：
- 详细的问题描述
- 重现步骤
- 浏览器版本和操作系统
- 扩展版本
- 控制台错误信息
- 网络环境信息

### 3. 联系支持
- **GitHub Issues**: [提交新问题](https://github.com/lingocub/extension/issues)
- **讨论区**: [参与讨论](https://github.com/lingocub/extension/discussions)
- **邮件支持**: <EMAIL>

## 🔗 有用链接

- **ElevenLabs状态页面**: https://status.elevenlabs.io
- **ElevenLabs文档**: https://docs.elevenlabs.io
- **Chrome扩展开发文档**: https://developer.chrome.com/docs/extensions/
- **项目GitHub页面**: https://github.com/lingocub/extension

## 💡 预防措施

为了避免常见问题：

1. **定期更新**
   - 保持扩展程序最新版本
   - 更新Chrome浏览器

2. **合理使用**
   - 不要同时处理多个视频
   - 选择合适的音频质量

3. **监控配额**
   - 定期检查API使用情况
   - 合理规划使用量

4. **备份设置**
   - 记录重要配置
   - 定期导出设置

---

**记住**：大多数问题都可以通过重新生成API密钥和刷新页面来解决！ 🔄
