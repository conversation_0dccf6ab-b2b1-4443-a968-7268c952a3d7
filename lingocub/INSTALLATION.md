# 📦 LingoCub 安装指南

本指南将帮助您快速安装和配置LingoCub Chrome扩展程序。

## 🔧 系统要求

### 浏览器要求
- **Chrome 88+** 或基于Chromium的浏览器（Edge、Brave等）
- 启用JavaScript
- 允许扩展程序权限

### 网络要求
- 稳定的互联网连接
- 能够访问YouTube和ElevenLabs API

### API要求
- 有效的ElevenLabs API密钥
- 足够的API配额用于配音服务

## 📥 安装步骤

### 方法1：从源码安装（推荐）

1. **下载源码**
   ```bash
   git clone https://github.com/your-username/lingocub.git
   cd lingocub
   ```

2. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开关启用开发者模式

4. **加载扩展程序**
   - 点击"加载已解压的扩展程序"按钮
   - 选择 `lingocub/extension` 文件夹
   - 点击"选择文件夹"

5. **验证安装**
   - 扩展程序应该出现在扩展列表中
   - 浏览器工具栏应该显示LingoCub图标

### 方法2：从Chrome Web Store安装

> 注意：此扩展程序目前尚未发布到Chrome Web Store

## 🔑 API密钥配置

### 获取ElevenLabs API密钥

1. **注册ElevenLabs账户**
   - 访问 [ElevenLabs官网](https://elevenlabs.io)
   - 点击"Sign Up"注册新账户
   - 验证邮箱并完成注册

2. **获取API密钥**
   - 登录后进入 [API页面](https://elevenlabs.io/app/speech-synthesis)
   - 在右上角找到您的API密钥
   - 点击复制按钮复制密钥

3. **配置扩展程序**
   - 点击浏览器工具栏中的LingoCub图标
   - 在弹出窗口中找到"API Key"输入框
   - 粘贴您的API密钥
   - 密钥会自动保存到本地存储

### API密钥安全提示

⚠️ **重要安全提醒**：
- 不要与他人分享您的API密钥
- 定期检查API使用情况
- 如果怀疑密钥泄露，请立即重新生成
- API密钥仅存储在本地，不会上传到任何服务器

## ⚙️ 初始配置

### 基本设置

1. **语言配置**
   - **源语言**：选择视频的原始语言（推荐使用"自动检测"）
   - **目标语言**：选择您希望配音的语言
   - 确保源语言和目标语言不同

2. **音频设置**
   - **说话人数量**：根据视频内容选择（推荐使用"自动检测"）
   - **音频质量**：
     - 标准：处理速度快，音质一般
     - 高质量：平衡的处理速度和音质（推荐）
     - 高级：最佳音质，处理时间较长

3. **保存设置**
   - 所有设置会自动保存
   - 下次使用时会自动加载上次的配置

## 🎯 首次使用

### 测试配音功能

1. **选择测试视频**
   - 打开YouTube
   - 选择一个较短的视频（建议1-3分钟）
   - 确保视频有清晰的语音内容

2. **启动配音**
   - 点击LingoCub扩展图标
   - 确认API密钥和语言设置
   - 点击"开始配音"按钮

3. **等待处理**
   - 观察状态指示器显示处理进度
   - 处理时间取决于视频长度和音质设置
   - 短视频通常需要30秒到2分钟

4. **享受配音**
   - 处理完成后，配音会自动开始播放
   - 原始音频会被自动静音
   - 使用切换开关可以在原始音频和配音之间切换

## 🔧 故障排除

### 常见问题

**问题1：扩展程序无法加载**
- 解决方案：
  - 确保Chrome版本为88或更高
  - 检查是否启用了开发者模式
  - 尝试重新加载扩展程序

**问题2：API密钥验证失败**
- 解决方案：
  - 检查API密钥是否正确复制
  - 确认ElevenLabs账户状态正常
  - 检查API配额是否充足

**问题3：配音处理失败**
- 解决方案：
  - 检查网络连接
  - 确认YouTube视频可以正常播放
  - 尝试选择不同的音质设置

**问题4：音频不同步**
- 解决方案：
  - 刷新YouTube页面
  - 重新启动配音
  - 检查浏览器是否有其他音频干扰

### 获取帮助

如果遇到其他问题：
1. 查看浏览器控制台错误信息
2. 在GitHub仓库提交Issue
3. 查看项目Wiki获取更多信息

## 📊 性能优化

### 提升配音体验

1. **网络优化**
   - 使用稳定的网络连接
   - 避免在网络高峰期使用
   - 考虑使用有线连接而非WiFi

2. **浏览器优化**
   - 关闭不必要的标签页
   - 禁用其他可能冲突的扩展
   - 定期清理浏览器缓存

3. **设置优化**
   - 根据需要选择合适的音质
   - 使用自动检测功能减少配置错误
   - 定期更新扩展程序

## 🔄 更新扩展

### 手动更新

1. 下载最新版本的源码
2. 在扩展管理页面点击"重新加载"按钮
3. 确认新功能正常工作

### 自动更新

> 注意：从源码安装的扩展不支持自动更新

## 🗑️ 卸载扩展

如需卸载LingoCub：

1. 打开 `chrome://extensions/`
2. 找到LingoCub扩展
3. 点击"移除"按钮
4. 确认卸载

卸载后，所有本地存储的设置和API密钥将被清除。

---

🎉 **恭喜！您已成功安装LingoCub！** 现在可以开始享受多语言YouTube视频配音体验了！
