#!/usr/bin/env node

/**
 * Manifest Validation Script for LingoCub Chrome Extension
 * Validates the manifest.json file for Chrome extension requirements
 */

const fs = require('fs');
const path = require('path');

class ManifestValidator {
    constructor() {
        this.manifestPath = path.join(__dirname, '../extension/manifest.json');
        this.errors = [];
        this.warnings = [];
    }

    // Main validation function
    validate() {
        console.log('🔍 Validating Chrome Extension Manifest...\n');

        try {
            // Check if manifest file exists
            if (!fs.existsSync(this.manifestPath)) {
                this.addError('Manifest file not found at: ' + this.manifestPath);
                return this.displayResults();
            }

            // Parse manifest
            const manifestContent = fs.readFileSync(this.manifestPath, 'utf8');
            const manifest = JSON.parse(manifestContent);

            // Run validation checks
            this.validateBasicStructure(manifest);
            this.validatePermissions(manifest);
            this.validateContentScripts(manifest);
            this.validateIcons(manifest);
            this.validateBackground(manifest);
            this.validateAction(manifest);
            this.validateCommands(manifest);
            this.validateWebAccessibleResources(manifest);

            return this.displayResults();

        } catch (error) {
            this.addError(`Failed to parse manifest.json: ${error.message}`);
            return this.displayResults();
        }
    }

    // Validate basic manifest structure
    validateBasicStructure(manifest) {
        const requiredFields = [
            'manifest_version',
            'name',
            'version',
            'description'
        ];

        requiredFields.forEach(field => {
            if (!manifest[field]) {
                this.addError(`Missing required field: ${field}`);
            }
        });

        // Check manifest version
        if (manifest.manifest_version !== 3) {
            this.addError(`Invalid manifest_version: ${manifest.manifest_version}. Expected: 3`);
        }

        // Validate version format
        if (manifest.version && !/^\d+\.\d+\.\d+$/.test(manifest.version)) {
            this.addWarning(`Version format should be X.Y.Z: ${manifest.version}`);
        }

        // Check name length
        if (manifest.name && manifest.name.length > 75) {
            this.addError(`Name too long (${manifest.name.length} chars). Max: 75`);
        }

        // Check description length
        if (manifest.description && manifest.description.length > 132) {
            this.addError(`Description too long (${manifest.description.length} chars). Max: 132`);
        }
    }

    // Validate permissions
    validatePermissions(manifest) {
        if (!manifest.permissions || !Array.isArray(manifest.permissions)) {
            this.addWarning('No permissions specified');
            return;
        }

        const validPermissions = [
            'storage', 'tabs', 'activeTab', 'scripting', 'background',
            'contextMenus', 'notifications', 'webRequest', 'webRequestBlocking'
        ];

        manifest.permissions.forEach(permission => {
            if (!validPermissions.includes(permission) && !permission.startsWith('http')) {
                this.addWarning(`Unknown permission: ${permission}`);
            }
        });

        // Check for required permissions
        const requiredPermissions = ['storage', 'tabs', 'activeTab'];
        requiredPermissions.forEach(permission => {
            if (!manifest.permissions.includes(permission)) {
                this.addError(`Missing required permission: ${permission}`);
            }
        });
    }

    // Validate content scripts
    validateContentScripts(manifest) {
        if (!manifest.content_scripts || !Array.isArray(manifest.content_scripts)) {
            this.addError('No content scripts specified');
            return;
        }

        manifest.content_scripts.forEach((script, index) => {
            if (!script.matches || !Array.isArray(script.matches)) {
                this.addError(`Content script ${index}: missing matches`);
            }

            if (!script.js || !Array.isArray(script.js)) {
                this.addError(`Content script ${index}: missing js files`);
            }

            // Check if JS files exist
            if (script.js) {
                script.js.forEach(jsFile => {
                    const filePath = path.join(__dirname, '../extension', jsFile);
                    if (!fs.existsSync(filePath)) {
                        this.addError(`Content script file not found: ${jsFile}`);
                    }
                });
            }
        });
    }

    // Validate icons
    validateIcons(manifest) {
        if (!manifest.icons) {
            this.addWarning('No icons specified');
            return;
        }

        const requiredSizes = ['16', '32', '48', '128'];
        requiredSizes.forEach(size => {
            if (!manifest.icons[size]) {
                this.addError(`Missing icon size: ${size}x${size}`);
            } else {
                const iconPath = path.join(__dirname, '../extension', manifest.icons[size]);
                if (!fs.existsSync(iconPath)) {
                    this.addError(`Icon file not found: ${manifest.icons[size]}`);
                }
            }
        });
    }

    // Validate background script
    validateBackground(manifest) {
        if (!manifest.background) {
            this.addError('No background script specified');
            return;
        }

        if (!manifest.background.service_worker) {
            this.addError('Missing service_worker in background');
        } else {
            const bgPath = path.join(__dirname, '../extension', manifest.background.service_worker);
            if (!fs.existsSync(bgPath)) {
                this.addError(`Background script not found: ${manifest.background.service_worker}`);
            }
        }
    }

    // Validate action (popup)
    validateAction(manifest) {
        if (!manifest.action) {
            this.addWarning('No action (popup) specified');
            return;
        }

        if (manifest.action.default_popup) {
            const popupPath = path.join(__dirname, '../extension', manifest.action.default_popup);
            if (!fs.existsSync(popupPath)) {
                this.addError(`Popup file not found: ${manifest.action.default_popup}`);
            }
        }
    }

    // Validate commands (keyboard shortcuts)
    validateCommands(manifest) {
        if (manifest.commands) {
            Object.keys(manifest.commands).forEach(command => {
                const cmd = manifest.commands[command];
                if (!cmd.description) {
                    this.addWarning(`Command ${command}: missing description`);
                }
            });
        }
    }

    // Validate web accessible resources
    validateWebAccessibleResources(manifest) {
        if (manifest.web_accessible_resources) {
            if (!Array.isArray(manifest.web_accessible_resources)) {
                this.addError('web_accessible_resources must be an array');
            }
        }
    }

    // Add error
    addError(message) {
        this.errors.push(message);
    }

    // Add warning
    addWarning(message) {
        this.warnings.push(message);
    }

    // Display validation results
    displayResults() {
        console.log('📋 Validation Results:');
        console.log('=====================\n');

        if (this.errors.length > 0) {
            console.log('❌ Errors:');
            this.errors.forEach(error => {
                console.log(`   • ${error}`);
            });
            console.log('');
        }

        if (this.warnings.length > 0) {
            console.log('⚠️  Warnings:');
            this.warnings.forEach(warning => {
                console.log(`   • ${warning}`);
            });
            console.log('');
        }

        const hasErrors = this.errors.length > 0;
        const hasWarnings = this.warnings.length > 0;

        if (!hasErrors && !hasWarnings) {
            console.log('✅ Manifest validation passed! No issues found.');
        } else if (!hasErrors) {
            console.log('✅ Manifest validation passed with warnings.');
        } else {
            console.log('❌ Manifest validation failed. Please fix the errors above.');
        }

        console.log(`\n📊 Summary: ${this.errors.length} errors, ${this.warnings.length} warnings`);

        return {
            success: this.errors.length === 0,
            errors: this.errors,
            warnings: this.warnings
        };
    }
}

// Run validation if script is executed directly
if (require.main === module) {
    const validator = new ManifestValidator();
    const result = validator.validate();
    process.exit(result.success ? 0 : 1);
}

module.exports = ManifestValidator;
