/* Importing Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

/* CSS Variables for Consistent Theming */
:root {
  --primary-color: #4a90e2;
  --primary-hover: #357ab8;
  --secondary-color: #ffffff;
  --background-color: #f9f9f9;
  --container-bg: #ffffff;
  --border-color: #e0e0e0;
  --text-color: #333333;
  --label-color: #555555;
  --status-bg: #e9ecef;
  --status-text: #4a90e2;
  --font-family: 'Roboto', Arial, sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  width: 350px;
  padding: 20px;
  background-color: var(--background-color);
  display: flex;
  justify-content: center;
  align-items: center;
}
/* styles.css */
body {
  width: 400px;
  height: 760px;
  overflow-y: auto;
}


#container {
  padding: 25px 30px;
  width: 100%;
}

h2 {
  font-size: 24px;
  color: var(--text-color);
  text-align: center;
  margin-bottom: 25px;
  font-weight: 500;
}

form {
  display: flex;
  flex-direction: column;
}

label {
  margin-top: 20px;
  font-weight: 500;
  color: var(--label-color);
}

input, select {
  margin-bottom: 10px;
}

input[type="password"],
select {
  width: 100%;
  padding: 12px 15px;
  margin-top: 8px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 15px;
  transition: border-color 0.3s, box-shadow 0.3s;
}

input[type="text"]:focus,
select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 5px rgba(74, 144, 226, 0.5);
  outline: none;
}

button {
  width: 100%;
  padding: 14px;
  margin-top: 25px;
  font-size: 16px;
  font-weight: 500;
  color: var(--secondary-color);
  background-color: var(--primary-color);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
}

button:hover {
  background-color: var(--primary-hover);
}

button:active {
  transform: scale(0.98);
}

#status-container {
  margin-top: 30px;
  padding: 12px;
  background-color: var(--status-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  text-align: center;
  color: var(--status-text);
  font-weight: 500;
}

.status-default {
  color: var(--status-text); /* Default color as defined earlier */
}

.status-dubbed {
  color: #28a745; /* Green color for 'Dubbed' status */
}

.status-in-progress {
  color: #ffc107; /* Amber color for 'In Progress' status */
}

.status-error {
  color: #dc3545; /* Red color for 'Error' status */
}

/* Optional: Add transitions for smooth color changes */
#status-container {
  transition: background-color 0.3s, color 0.3s;
}

/* Input Focus Transitions */
input[type="text"]:focus,
select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 5px rgba(74, 144, 226, 0.5);
  transition: border-color 0.3s, box-shadow 0.3s;
}

/* Responsive Design */
@media (max-width: 400px) {
  h2 {
    font-size: 22px;
  }

  button {
    font-size: 15px;
  }
}

/* Toggle Switch Container */
#dubbedAudioToggle-container {
  display: flex;
  align-items: center;
  margin-top: 10px;
  justify-content: center;
}

.toggle-switch {
  position: relative;
  width: 40px;
  height: 20px;
  background-color: var(--border-color);
  border-radius: 15px;
  transition: background-color 0.3s;
  cursor: pointer;
}

.toggle-switch::before {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  background-color: var(--secondary-color);
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: transform 0.3s;
}

/* Custom Checkbox Appearance */
.custom-checkbox {
  display: none;
}

.custom-checkbox:checked + .toggle-switch {
  background-color: var(--primary-color);
}

.custom-checkbox:checked + .toggle-switch::before {
  transform: translateX(20px);
}

/* Checkmark Symbol */
.custom-checkbox:checked + .toggle-switch::after {
  content: "";
  position: absolute;
  color: var(--secondary-color);
  font-size: 12px;
  top: 3px;
  right: 6px;
}

/* Toggle Label Style */
.toggle-label {
  font-size: 15px;
  color: var(--label-color);
  font-weight: 500;
  margin-left: 10px;
  cursor: pointer;
}

#disclaimer {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
  color: #333;
}

#disclaimer p {
  margin: 0;
  line-height: 1.4;
}

#disclaimer strong {
  color: #dc3545; /* Red color to emphasize the word "Note" */
}
