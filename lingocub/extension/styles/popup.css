/* Importing Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables for Enhanced Theming */
:root {
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --primary-light: #e0e7ff;
  --secondary-color: #ffffff;
  --accent-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #f8fafc;
  --container-bg: #ffffff;
  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;
  --text-color: #1e293b;
  --text-secondary: #64748b;
  --label-color: #374151;
  --status-bg: #f1f5f9;
  --status-text: #6366f1;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  width: 420px;
  min-height: 600px;
  max-height: 800px;
  background: linear-gradient(135deg, var(--background-color) 0%, #e2e8f0 100%);
  overflow-y: auto;
  line-height: 1.5;
  color: var(--text-color);
}

/* Scrollbar Styling */
body::-webkit-scrollbar {
  width: 6px;
}

body::-webkit-scrollbar-track {
  background: var(--background-color);
}

body::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

body::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

#container {
  padding: 24px;
  width: 100%;
  background: var(--container-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  margin: 12px;
}

/* Header Styles */
.header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--primary-light);
}

.header h2 {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8px;
  letter-spacing: -0.025em;
}

.subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 400;
}

/* Section Styles */
.section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  padding: 12px 0;
  font-weight: 600;
  color: var(--text-color);
  transition: var(--transition);
}

.section-header:hover {
  color: var(--primary-color);
}

.expand-icon {
  transition: transform 0.3s ease;
}

.section-header.expanded .expand-icon {
  transform: rotate(180deg);
}

/* Label Styles */
label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--label-color);
  font-size: 14px;
}

/* Input Group */
.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-group input {
  flex: 1;
  padding-right: 45px;
}

.toggle-btn {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.toggle-btn:hover {
  background: var(--primary-light);
}

/* Enhanced Input Styles */
input[type="password"],
input[type="text"],
select {
  width: 100%;
  padding: 14px 16px;
  margin-top: 4px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 15px;
  font-family: var(--font-family);
  background: var(--secondary-color);
  transition: var(--transition);
  color: var(--text-color);
}

input[type="password"]:focus,
input[type="text"]:focus,
select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  outline: none;
  transform: translateY(-1px);
}

/* Select Wrapper */
.select-wrapper {
  position: relative;
}

.select-wrapper::after {
  content: '▼';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  color: var(--text-secondary);
  font-size: 12px;
}

select {
  appearance: none;
  background-image: none;
  cursor: pointer;
}

/* API Key Actions */
.api-key-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  gap: 12px;
}

.test-btn {
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  background: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
}

.test-btn:hover {
  background: var(--primary-color);
  color: var(--secondary-color);
}

.test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* API Status */
.api-status {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
  transition: var(--transition);
}

.api-status.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--accent-color);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.api-status.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.api-status.testing {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(99, 102, 241, 0.3);
}

/* Help Text */
.help-text {
  font-size: 12px;
  flex: 1;
}

.help-text a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.help-text a:hover {
  text-decoration: underline;
}

/* Button Styles */
.button-group {
  display: flex;
  gap: 12px;
  margin: 24px 0;
}

.primary-btn,
.secondary-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 20px;
  font-size: 15px;
  font-weight: 600;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition);
  font-family: var(--font-family);
  position: relative;
  overflow: hidden;
}

.primary-btn {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: var(--secondary-color);
  box-shadow: var(--shadow-md);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.primary-btn:active {
  transform: translateY(0);
}

.secondary-btn {
  background: var(--secondary-color);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.secondary-btn:hover {
  background: var(--primary-light);
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 16px;
}

.btn-text {
  font-weight: 600;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  justify-content: space-around;
  gap: 8px;
  padding: 16px 0;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.action-btn {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  background: var(--secondary-color);
  border: 2px solid var(--border-color);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.action-btn:hover {
  background: var(--primary-light);
  border-color: var(--primary-color);
  transform: scale(1.1);
}

/* Enhanced Status Container */
#status-container {
  background: var(--status-bg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 16px;
  transition: var(--transition);
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.status-icon {
  font-size: 18px;
}

.status-label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 14px;
}

#status-content {
  text-align: center;
}

#status-text {
  font-weight: 600;
  font-size: 15px;
  transition: var(--transition);
}

.status-default {
  color: var(--text-secondary);
}

.status-dubbed {
  color: var(--accent-color);
}

.status-in-progress {
  color: var(--warning-color);
}

.status-error {
  color: var(--error-color);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--border-color);
  border-radius: 3px;
  margin-top: 12px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 3px;
  width: 0%;
  transition: width 0.3s ease;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Enhanced Toggle Switch */
#dubbedAudioToggle-container {
  background: var(--secondary-color);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 16px;
}

.toggle-group {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toggle-switch {
  position: relative;
  width: 52px;
  height: 28px;
  background-color: var(--border-color);
  border-radius: 14px;
  transition: var(--transition);
  cursor: pointer;
  flex-shrink: 0;
}

.toggle-switch::before {
  content: "";
  position: absolute;
  width: 24px;
  height: 24px;
  background-color: var(--secondary-color);
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.custom-checkbox {
  display: none;
}

.custom-checkbox:checked + .toggle-switch {
  background-color: var(--primary-color);
}

.custom-checkbox:checked + .toggle-switch::before {
  transform: translateX(24px);
}

.toggle-labels {
  flex: 1;
}

.toggle-label {
  display: block;
  font-size: 15px;
  color: var(--text-color);
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 4px;
}

.toggle-description {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--secondary-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.close-modal {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 4px;
  border-radius: var(--radius-sm);
  transition: var(--transition);
}

.close-modal:hover {
  background: var(--primary-light);
  color: var(--primary-color);
}

.modal-body {
  padding: 20px 24px;
}

/* Keyboard Shortcuts */
.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-color);
}

.shortcut-item:last-child {
  border-bottom: none;
}

kbd {
  background: var(--status-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  font-family: monospace;
  color: var(--text-color);
}

/* Help Content */
.help-content {
  background: var(--status-bg);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-top: 12px;
}

.tip-item {
  margin-bottom: 16px;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-item strong {
  display: block;
  color: var(--primary-color);
  margin-bottom: 8px;
  font-size: 14px;
}

.tip-item p {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Footer */
.footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
  margin-top: 24px;
}

.footer p {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.footer-links {
  font-size: 11px;
}

.footer-links a {
  color: var(--primary-color);
  text-decoration: none;
  margin: 0 4px;
}

.footer-links a:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 450px) {
  body {
    width: 100%;
    margin: 0;
  }

  #container {
    margin: 8px;
    padding: 16px;
  }

  .header h2 {
    font-size: 24px;
  }

  .button-group {
    flex-direction: column;
  }

  .quick-actions {
    justify-content: space-between;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Disabled States */
.disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

/* Focus Styles for Accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-color: #000000;
    --background-color: #ffffff;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
