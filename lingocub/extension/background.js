// Enhanced background.js with improved error handling and features

// Global state management
const DubbingManager = {
    activeSessions: new Map(),
    retryAttempts: new Map(),
    maxRetries: 3,

    addSession(dubbingId, sessionData) {
        this.activeSessions.set(dubbingId, sessionData);
        this.retryAttempts.set(dubbingId, 0);
    },

    removeSession(dubbingId) {
        this.activeSessions.delete(dubbingId);
        this.retryAttempts.delete(dubbingId);
    },

    getSession(dubbingId) {
        return this.activeSessions.get(dubbingId);
    },

    incrementRetry(dubbingId) {
        const current = this.retryAttempts.get(dubbingId) || 0;
        this.retryAttempts.set(dubbingId, current + 1);
        return current + 1;
    },

    canRetry(dubbingId) {
        return (this.retryAttempts.get(dubbingId) || 0) < this.maxRetries;
    }
};

// Enhanced message listener with better command handling
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("[Background] Received message:", message.command);

    switch (message.command) {
        case "startDubbing":
            handleStartDubbing(message, sendResponse);
            return true; // Keep message channel open for async response

        case "stopDubbing":
            handleStopDubbing(message, sendResponse);
            return true;

        case "getDubbingStatus":
            handleGetDubbingStatus(message, sendResponse);
            return true;

        default:
            console.warn("[Background] Unknown command:", message.command);
            sendResponse({ success: false, error: "Unknown command" });
    }
});

// Enhanced start dubbing handler
async function handleStartDubbing(message, sendResponse) {
    try {
        const data = await chrome.storage.local.get(["apiKey", "previousDubbingId"]);
        const { apiKey, previousDubbingId } = data;
        const { videoUrl, sourceLanguage, targetLanguage, numSpeakers, audioQuality, tabId } = message;

        if (!apiKey) {
            const errorMsg = "Please enter your ElevenLabs API key in the extension popup.";
            notifyTab(tabId, "showAlert", errorMsg);
            sendResponse({ success: false, error: "API key not provided." });
            return;
        }

        // Validate YouTube URL
        if (!isValidYouTubeUrl(videoUrl)) {
            const errorMsg = "Please navigate to a valid YouTube video page.";
            notifyTab(tabId, "showAlert", errorMsg);
            sendResponse({ success: false, error: "Invalid YouTube URL." });
            return;
        }

        // Clean up previous dubbing if exists
        if (previousDubbingId) {
            try {
                await deletePreviousDubbing(previousDubbingId, apiKey);
                await chrome.storage.local.remove("previousDubbingId");
                console.log("[Background] Previous dubbing cleaned up");
            } catch (error) {
                console.warn("[Background] Error cleaning up previous dubbing:", error);
                // Continue anyway
            }
        }

        // Start new dubbing session
        const dubbingResult = await startDubbing(videoUrl, apiKey, sourceLanguage, targetLanguage, numSpeakers, audioQuality, tabId);

        if (dubbingResult.success) {
            DubbingManager.addSession(dubbingResult.dubbingId, {
                tabId,
                videoUrl,
                sourceLanguage,
                targetLanguage,
                numSpeakers,
                audioQuality,
                startTime: Date.now()
            });
        }

        sendResponse(dubbingResult);

    } catch (error) {
        console.error("[Background] Error in handleStartDubbing:", error);
        sendResponse({ success: false, error: error.message || "An unexpected error occurred." });
    }
}

// Stop dubbing handler
async function handleStopDubbing(message, sendResponse) {
    try {
        const { dubbingId } = message;

        if (dubbingId && DubbingManager.getSession(dubbingId)) {
            const sessionData = DubbingManager.getSession(dubbingId);

            // Attempt to delete the dubbing
            try {
                const data = await chrome.storage.local.get(["apiKey"]);
                await deletePreviousDubbing(dubbingId, data.apiKey);
            } catch (error) {
                console.warn("[Background] Error deleting dubbing:", error);
            }

            // Clean up session
            DubbingManager.removeSession(dubbingId);

            // Notify tab
            if (sessionData.tabId) {
                notifyTab(sessionData.tabId, "dubbingError", "Dubbing stopped by user");
            }
        }

        sendResponse({ success: true });

    } catch (error) {
        console.error("[Background] Error stopping dubbing:", error);
        sendResponse({ success: false, error: error.message });
    }
}

// Get dubbing status handler
async function handleGetDubbingStatus(message, sendResponse) {
    try {
        const { dubbingId } = message;
        const sessionData = DubbingManager.getSession(dubbingId);

        if (!sessionData) {
            sendResponse({ success: false, error: "Session not found" });
            return;
        }

        const data = await chrome.storage.local.get(["apiKey"]);
        const status = await checkDubbingStatusOnce(dubbingId, data.apiKey);

        sendResponse({ success: true, status });

    } catch (error) {
        console.error("[Background] Error getting dubbing status:", error);
        sendResponse({ success: false, error: error.message });
    }
}

// Utility function to validate YouTube URLs
function isValidYouTubeUrl(url) {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname.includes('youtube.com') && urlObj.pathname === '/watch' && urlObj.searchParams.has('v');
    } catch {
        return false;
    }
}

// Utility function to notify tabs
function notifyTab(tabId, command, message, type = 'error') {
    chrome.tabs.sendMessage(tabId, {
        command,
        message,
        type
    }).catch(error => {
        console.warn("[Background] Error sending message to tab:", error);
    });
}

// Enhanced function to delete previous dubbing project
async function deletePreviousDubbing(dubbingId, apiKey) {
    try {
        console.log(`[Background] Deleting dubbing ID: ${dubbingId}`);

        const response = await fetch(`https://api.elevenlabs.io/v1/dubbing/${dubbingId}`, {
            method: "DELETE",
            headers: {
                "xi-api-key": apiKey,
                "Content-Type": "application/json"
            },
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to delete dubbing (${response.status}): ${errorText}`);
        }

        console.log(`[Background] Successfully deleted dubbing ID: ${dubbingId}`);
        return true;

    } catch (error) {
        console.error(`[Background] Error deleting dubbing ID ${dubbingId}:`, error);
        throw error;
    }
}

// Enhanced function to start dubbing with better error handling
async function startDubbing(sourceUrl, apiKey, sourceLanguage, targetLanguage, numSpeakers, audioQuality, tabId) {
    try {
        console.log(`[Background] Starting dubbing for: ${sourceUrl}`);

        // Prepare form data with enhanced parameters
        const formData = new FormData();
        formData.append("source_url", sourceUrl);
        formData.append("target_lang", targetLanguage);
        formData.append("source_lang", sourceLanguage);
        formData.append("num_speakers", numSpeakers);
        formData.append("watermark", "false");

        // Add quality settings based on audioQuality parameter
        switch (audioQuality) {
            case "premium":
                formData.append("highest_resolution", "true");
                break;
            case "high":
                formData.append("highest_resolution", "false");
                break;
            case "standard":
            default:
                // Use default settings
                break;
        }

        const response = await fetch("https://api.elevenlabs.io/v1/dubbing", {
            method: "POST",
            headers: {
                "xi-api-key": apiKey
            },
            body: formData,
        });

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.detail || data.error || `HTTP ${response.status}: ${response.statusText}`);
        }

        if (data.dubbing_id) {
            console.log(`[Background] Dubbing started with ID: ${data.dubbing_id}`);

            // Save dubbing info
            await chrome.storage.local.set({
                previousDubbingId: data.dubbing_id,
                dubbingStatus: "dubbing",
                dubbingStartTime: Date.now()
            });

            // Start monitoring dubbing status
            checkDubbingStatus(data.dubbing_id, apiKey, targetLanguage, tabId);

            return {
                success: true,
                dubbingId: data.dubbing_id,
                message: "Dubbing started successfully"
            };
        } else {
            throw new Error("No dubbing ID received from API");
        }

    } catch (error) {
        console.error("[Background] Error starting dubbing:", error);

        // Notify tab of error
        notifyTab(tabId, "showAlert", `Dubbing failed: ${error.message}`);

        return {
            success: false,
            error: error.message || "An error occurred while starting dubbing"
        };
    }
}

// Enhanced function to check dubbing status with better error handling and retry logic
function checkDubbingStatus(dubbingId, apiKey, targetLanguage, tabId) {
    console.log(`[Background] Starting status monitoring for dubbing ID: ${dubbingId}`);

    let checkCount = 0;
    const maxChecks = 300; // 5 minutes maximum (300 seconds)
    const checkInterval = 2000; // Check every 2 seconds

    const interval = setInterval(async () => {
        checkCount++;

        try {
            const statusData = await checkDubbingStatusOnce(dubbingId, apiKey);

            // Update popup with current status
            chrome.runtime.sendMessage({
                command: "updateStatus",
                status: statusData.status,
                progress: calculateProgress(statusData.status, checkCount, maxChecks)
            });

            switch (statusData.status) {
                case "dubbed":
                    clearInterval(interval);
                    DubbingManager.removeSession(dubbingId);

                    await chrome.storage.local.set({ dubbingStatus: "dubbed" });

                    // Fetch and play dubbed audio
                    await fetchDubbedAudio(dubbingId, targetLanguage, apiKey, tabId);

                    // Notify completion
                    chrome.runtime.sendMessage({ command: "dubbingComplete" });
                    console.log(`[Background] Dubbing completed for ID: ${dubbingId}`);
                    break;

                case "failed":
                    clearInterval(interval);
                    await handleDubbingFailure(dubbingId, statusData.error, tabId);
                    break;

                case "dubbing":
                case "processing":
                    // Continue monitoring
                    console.log(`[Background] Dubbing in progress (${checkCount}/${maxChecks})`);
                    break;

                default:
                    console.warn(`[Background] Unknown dubbing status: ${statusData.status}`);
            }

            // Timeout check
            if (checkCount >= maxChecks) {
                clearInterval(interval);
                await handleDubbingTimeout(dubbingId, tabId);
            }

        } catch (error) {
            console.error(`[Background] Error checking dubbing status:`, error);

            // Retry logic
            if (DubbingManager.canRetry(dubbingId)) {
                const retryCount = DubbingManager.incrementRetry(dubbingId);
                console.log(`[Background] Retrying status check (${retryCount}/${DubbingManager.maxRetries})`);
            } else {
                clearInterval(interval);
                await handleDubbingError(dubbingId, error.message, tabId);
            }
        }
    }, checkInterval);
}

// Function to check dubbing status once (for reuse)
async function checkDubbingStatusOnce(dubbingId, apiKey) {
    const response = await fetch(`https://api.elevenlabs.io/v1/dubbing/${dubbingId}`, {
        headers: {
            "xi-api-key": apiKey,
            "Content-Type": "application/json"
        }
    });

    if (!response.ok) {
        throw new Error(`Status check failed (${response.status}): ${response.statusText}`);
    }

    return await response.json();
}

// Calculate progress based on status and time
function calculateProgress(status, checkCount, maxChecks) {
    switch (status) {
        case "dubbing":
        case "processing":
            return Math.min(90, (checkCount / maxChecks) * 100);
        case "dubbed":
            return 100;
        case "failed":
            return 0;
        default:
            return (checkCount / maxChecks) * 50;
    }
}

// Handle dubbing failure with retry option
async function handleDubbingFailure(dubbingId, error, tabId) {
    console.error(`[Background] Dubbing failed for ID ${dubbingId}:`, error);

    DubbingManager.removeSession(dubbingId);
    await chrome.storage.local.set({ dubbingStatus: "failed" });

    const errorMessage = error || "Dubbing failed. Please try again.";
    notifyTab(tabId, "showAlert", errorMessage);
    chrome.runtime.sendMessage({ command: "dubbingError", error: errorMessage });
}

// Handle dubbing timeout
async function handleDubbingTimeout(dubbingId, tabId) {
    console.warn(`[Background] Dubbing timeout for ID ${dubbingId}`);

    DubbingManager.removeSession(dubbingId);
    await chrome.storage.local.set({ dubbingStatus: "timeout" });

    const timeoutMessage = "Dubbing is taking longer than expected. Please try again.";
    notifyTab(tabId, "showAlert", timeoutMessage);
    chrome.runtime.sendMessage({ command: "dubbingError", error: timeoutMessage });
}

// Handle dubbing error
async function handleDubbingError(dubbingId, error, tabId) {
    console.error(`[Background] Dubbing error for ID ${dubbingId}:`, error);

    DubbingManager.removeSession(dubbingId);
    await chrome.storage.local.set({ dubbingStatus: "error" });

    notifyTab(tabId, "showAlert", `An error occurred: ${error}`);
    chrome.runtime.sendMessage({ command: "dubbingError", error });
}

// Enhanced function to fetch dubbed audio with better error handling
async function fetchDubbedAudio(dubbingId, languageCode, apiKey, tabId) {
    try {
        console.log(`[Background] Fetching dubbed audio for ID: ${dubbingId}, language: ${languageCode}`);

        // Use the proxy URL to fetch audio
        const proxyUrl = `https://lingocub.vercel.app/api/server?dubbingId=${dubbingId}&languageCode=${languageCode}&apiKey=${apiKey}`;

        // Test the proxy URL first
        const testResponse = await fetch(proxyUrl, { method: 'HEAD' });
        if (!testResponse.ok) {
            throw new Error(`Audio not available (${testResponse.status})`);
        }

        // Send audio URL to content script
        chrome.tabs.sendMessage(tabId, {
            command: "playDubbedAudio",
            audioUrl: proxyUrl,
            dubbingId: dubbingId,
            languageCode: languageCode
        });

        console.log(`[Background] Audio URL sent to content script`);

    } catch (error) {
        console.error(`[Background] Error fetching dubbed audio:`, error);
        notifyTab(tabId, "showAlert", `Failed to load dubbed audio: ${error.message}`);
    }
}