// Enhanced popup.js with improved UI interactions and features

let dotInterval; // Interval for the animated dots
let progressInterval; // Interval for progress bar animation
let currentDubbingId = null; // Track current dubbing session

// Enhanced status management
const StatusManager = {
    element: null,
    progressBar: null,

    init() {
        this.element = document.getElementById("status-text");
        this.progressBar = document.getElementById("progress-bar");
    },

    update(status, progress = 0) {
        if (!this.element) return;

        // Clear existing intervals
        if (dotInterval) {
            clearInterval(dotInterval);
            dotInterval = null;
        }
        if (progressInterval) {
            clearInterval(progressInterval);
            progressInterval = null;
        }

        // Remove all status classes
        this.element.classList.remove("status-default", "status-dubbed", "status-in-progress", "status-error");

        const statusLower = status.toLowerCase();

        switch (statusLower) {
            case "dubbing":
            case "processing":
                this.animateDubbing();
                this.showProgress(true);
                this.animateProgress();
                this.element.classList.add("status-in-progress");
                break;

            case "dubbed":
            case "completed":
                this.element.textContent = "✅ Dubbing completed!";
                this.element.classList.add("status-dubbed");
                this.showProgress(false);
                this.enableAudioToggle();
                this.showSuccessActions();
                break;

            case "error":
            case "failed":
                this.element.textContent = "❌ Dubbing failed";
                this.element.classList.add("status-error");
                this.showProgress(false);
                this.showRetryOption();
                break;

            default:
                this.element.textContent = status;
                this.element.classList.add("status-default");
                this.showProgress(false);
        }
    },

    animateDubbing() {
        let dotCount = 0;
        dotInterval = setInterval(() => {
            dotCount = (dotCount + 1) % 4;
            const dots = '.'.repeat(dotCount);
            this.element.textContent = `🎬 Processing${dots}`;
        }, 500);
    },

    showProgress(show) {
        if (this.progressBar) {
            this.progressBar.style.display = show ? 'block' : 'none';
        }
    },

    animateProgress() {
        if (!this.progressBar) return;

        const progressFill = this.progressBar.querySelector('.progress-fill');
        if (!progressFill) return;

        let progress = 0;
        progressInterval = setInterval(() => {
            progress = (progress + 1) % 101;
            progressFill.style.width = `${progress}%`;
        }, 100);
    },

    enableAudioToggle() {
        const toggle = document.getElementById("dubbedAudioToggle");
        if (toggle) {
            toggle.checked = true;
            chrome.storage.local.set({ audioToggleState: true });
        }
    },

    showSuccessActions() {
        const downloadBtn = document.getElementById("downloadAudio");
        if (downloadBtn) {
            downloadBtn.style.display = 'block';
        }
    },

    showRetryOption() {
        // Could add a retry button or message here
        console.log("Showing retry option");
    }
};

// UI Enhancement utilities
const UIUtils = {
    showNotification(message, type = 'info', duration = 4000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? 'var(--error-color)' : type === 'success' ? 'var(--accent-color)' : 'var(--primary-color)'};
            color: white;
            padding: 12px 20px;
            border-radius: var(--radius-md);
            z-index: 10000;
            font-size: 14px;
            box-shadow: var(--shadow-lg);
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
        `;

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, duration);
    },

    toggleLoading(element, loading) {
        if (loading) {
            element.classList.add('loading', 'disabled');
        } else {
            element.classList.remove('loading', 'disabled');
        }
    },

    animateElement(element, animation = 'fade-in') {
        element.classList.add(animation);
        setTimeout(() => element.classList.remove(animation), 300);
    }
};

// Enhanced DOM Content Loaded with new UI features
document.addEventListener("DOMContentLoaded", () => {
    // Initialize status manager
    StatusManager.init();

    // Load saved settings
    chrome.storage.local.get(
        ["apiKey", "sourceLanguage", "targetLanguage", "numSpeakers", "audioQuality", "dubbingStatus", "audioToggleState"],
        (data) => {
            // Populate form fields
            if (data.apiKey) document.getElementById("apiKey").value = data.apiKey;
            if (data.sourceLanguage) document.getElementById("sourceLanguage").value = data.sourceLanguage;
            if (data.targetLanguage) document.getElementById("targetLanguage").value = data.targetLanguage;
            if (data.numSpeakers !== undefined) document.getElementById("numSpeakers").value = data.numSpeakers;
            if (data.audioQuality) document.getElementById("audioQuality").value = data.audioQuality;

            // Set initial status
            const initialStatus = data.dubbingStatus || "Ready to start";
            StatusManager.update(initialStatus);

            // Set audio toggle state
            document.getElementById("dubbedAudioToggle").checked = data.audioToggleState !== false;

            // Animate container on load
            UIUtils.animateElement(document.getElementById("container"), "slide-up");
        }
    );

    // Setup event listeners for new UI elements
    setupEventListeners();

    // Check if we're on a YouTube page
    checkYouTubePage();
});

// Setup all event listeners
function setupEventListeners() {
    // API Key toggle visibility
    const toggleApiKeyBtn = document.getElementById("toggleApiKey");
    const apiKeyInput = document.getElementById("apiKey");

    if (toggleApiKeyBtn && apiKeyInput) {
        toggleApiKeyBtn.addEventListener("click", () => {
            const isPassword = apiKeyInput.type === "password";
            apiKeyInput.type = isPassword ? "text" : "password";
            toggleApiKeyBtn.textContent = isPassword ? "🙈" : "👁️";
        });
    }

    // Help section toggle
    const helpToggle = document.getElementById("help-toggle");
    const helpContent = document.getElementById("help-content");

    if (helpToggle && helpContent) {
        helpToggle.addEventListener("click", () => {
            const isExpanded = helpContent.style.display !== "none";
            helpContent.style.display = isExpanded ? "none" : "block";
            helpToggle.classList.toggle("expanded", !isExpanded);
            UIUtils.animateElement(helpContent, "fade-in");
        });
    }

    // Quick action buttons
    setupQuickActions();

    // Keyboard shortcuts modal
    setupShortcutsModal();

    // Form validation
    setupFormValidation();
}

// Setup quick action buttons
function setupQuickActions() {
    // Refresh page button
    const refreshBtn = document.getElementById("refreshPage");
    if (refreshBtn) {
        refreshBtn.addEventListener("click", () => {
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                chrome.tabs.reload(tabs[0].id);
                UIUtils.showNotification("Page refreshed!", "success");
            });
        });
    }

    // Download audio button
    const downloadBtn = document.getElementById("downloadAudio");
    if (downloadBtn) {
        downloadBtn.addEventListener("click", () => {
            // Implementation for downloading dubbed audio
            UIUtils.showNotification("Download feature coming soon!", "info");
        });
    }

    // Show shortcuts button
    const shortcutsBtn = document.getElementById("showShortcuts");
    if (shortcutsBtn) {
        shortcutsBtn.addEventListener("click", () => {
            document.getElementById("shortcuts-modal").style.display = "flex";
        });
    }

    // Help button
    const helpBtn = document.getElementById("showHelp");
    if (helpBtn) {
        helpBtn.addEventListener("click", () => {
            chrome.tabs.create({ url: "https://github.com/your-repo/lingocub/wiki" });
        });
    }
}

// Setup shortcuts modal
function setupShortcutsModal() {
    const modal = document.getElementById("shortcuts-modal");
    const closeBtn = modal?.querySelector(".close-modal");

    if (closeBtn) {
        closeBtn.addEventListener("click", () => {
            modal.style.display = "none";
        });
    }

    // Close modal when clicking outside
    if (modal) {
        modal.addEventListener("click", (e) => {
            if (e.target === modal) {
                modal.style.display = "none";
            }
        });
    }
}

// Form validation
function setupFormValidation() {
    const apiKeyInput = document.getElementById("apiKey");
    const sourceSelect = document.getElementById("sourceLanguage");
    const targetSelect = document.getElementById("targetLanguage");

    // Real-time validation
    if (apiKeyInput) {
        apiKeyInput.addEventListener("input", validateApiKeyFormat);
    }

    if (sourceSelect && targetSelect) {
        [sourceSelect, targetSelect].forEach(select => {
            select.addEventListener("change", validateLanguageSelection);
        });
    }
}

// Validate API key format
function validateApiKeyFormat() {
    const apiKey = document.getElementById("apiKey").value.trim();
    const isValid = apiKey.length >= 10; // Basic validation

    const input = document.getElementById("apiKey");
    input.style.borderColor = isValid ? "var(--accent-color)" : "var(--error-color)";
}

// Validate language selection
function validateLanguageSelection() {
    const source = document.getElementById("sourceLanguage").value;
    const target = document.getElementById("targetLanguage").value;

    if (source === target && source !== "auto") {
        UIUtils.showNotification("Source and target languages should be different", "warning");
    }
}

// Check if current tab is YouTube
function checkYouTubePage() {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const currentTab = tabs[0];
        const isYouTube = currentTab.url && currentTab.url.includes("youtube.com/watch");

        const startBtn = document.getElementById("startDubbing");
        if (startBtn) {
            if (!isYouTube) {
                startBtn.textContent = "🔗 Go to YouTube";
                startBtn.onclick = () => {
                    chrome.tabs.create({ url: "https://youtube.com" });
                };
            }
        }
    });
}

// Enhanced toggle switch listener with better feedback
document.getElementById("dubbedAudioToggle").addEventListener("change", (event) => {
    const isDubbedOn = event.target.checked;
    chrome.storage.local.set({ audioToggleState: isDubbedOn });

    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        chrome.tabs.sendMessage(tabs[0].id, { command: "toggleAudio", dubbed: isDubbedOn }, (response) => {
            const message = isDubbedOn ? "Switched to dubbed audio" : "Switched to original audio";
            UIUtils.showNotification(message, "success");
        });
    });

    // Animate the toggle
    UIUtils.animateElement(event.target.parentElement, "fade-in");
});

// Enhanced "Start Dubbing" button with better validation and feedback
document.getElementById("startDubbing").addEventListener("click", async () => {
    const startBtn = document.getElementById("startDubbing");
    const apiKey = document.getElementById("apiKey").value.trim();
    const sourceLanguage = document.getElementById("sourceLanguage").value;
    const targetLanguage = document.getElementById("targetLanguage").value;
    const numSpeakers = parseInt(document.getElementById("numSpeakers").value) || 0;
    const audioQuality = document.getElementById("audioQuality").value;

    // Enhanced validation
    if (!apiKey) {
        UIUtils.showNotification("Please enter your ElevenLabs API key.", "error");
        document.getElementById("apiKey").focus();
        return;
    }

    if (sourceLanguage === targetLanguage && sourceLanguage !== "auto") {
        UIUtils.showNotification("Source and target languages should be different.", "warning");
        return;
    }

    // Show loading state
    UIUtils.toggleLoading(startBtn, true);
    startBtn.innerHTML = '<span class="btn-icon">⏳</span><span class="btn-text">Validating...</span>';

    try {
        // Validate API key
        const isValidKey = await validateApiKeyAsync(apiKey);

        if (!isValidKey) {
            UIUtils.showNotification("Invalid API key. Please check and try again.", "error");
            UIUtils.toggleLoading(startBtn, false);
            startBtn.innerHTML = '<span class="btn-icon">🎬</span><span class="btn-text">Start Dubbing</span>';
            return;
        }

        // Save settings
        chrome.storage.local.set({
            apiKey,
            sourceLanguage,
            targetLanguage,
            numSpeakers,
            audioQuality
        });

        // Check current tab
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            const activeTab = tabs[0];
            const url = activeTab.url;

            if (!url || !url.includes("youtube.com/watch")) {
                UIUtils.showNotification("Please navigate to a YouTube video page first.", "warning");
                UIUtils.toggleLoading(startBtn, false);
                startBtn.innerHTML = '<span class="btn-icon">🔗</span><span class="btn-text">Go to YouTube</span>';
                startBtn.onclick = () => chrome.tabs.create({ url: "https://youtube.com" });
                return;
            }

            // Start dubbing process
            StatusManager.update("Processing");
            startBtn.innerHTML = '<span class="btn-icon">🎬</span><span class="btn-text">Processing...</span>';

            chrome.runtime.sendMessage(
                {
                    command: "startDubbing",
                    videoUrl: url,
                    tabId: activeTab.id,
                    sourceLanguage,
                    targetLanguage,
                    numSpeakers,
                    audioQuality
                },
                (response) => {
                    UIUtils.toggleLoading(startBtn, false);

                    if (response && response.success) {
                        currentDubbingId = response.dubbingId;
                        UIUtils.showNotification("Dubbing started successfully!", "success");
                        startBtn.innerHTML = '<span class="btn-icon">⏹️</span><span class="btn-text">Stop Dubbing</span>';
                        startBtn.onclick = stopDubbing;

                        // Show stop button
                        const stopBtn = document.getElementById("stopDubbing");
                        if (stopBtn) {
                            stopBtn.style.display = "block";
                        }
                    } else {
                        console.error("Failed to start dubbing:", response?.error);
                        StatusManager.update("Error");
                        UIUtils.showNotification(response?.error || "Failed to start dubbing. Please try again.", "error");
                        startBtn.innerHTML = '<span class="btn-icon">🎬</span><span class="btn-text">Start Dubbing</span>';
                    }
                }
            );
        });

    } catch (error) {
        console.error("Error during dubbing setup:", error);
        UIUtils.showNotification("An unexpected error occurred. Please try again.", "error");
        UIUtils.toggleLoading(startBtn, false);
        startBtn.innerHTML = '<span class="btn-icon">🎬</span><span class="btn-text">Start Dubbing</span>';
    }
});

// Stop dubbing function
function stopDubbing() {
    if (currentDubbingId) {
        chrome.runtime.sendMessage({
            command: "stopDubbing",
            dubbingId: currentDubbingId
        }, (response) => {
            if (response && response.success) {
                UIUtils.showNotification("Dubbing stopped", "info");
                resetDubbingUI();
            }
        });
    } else {
        // Just reset UI if no active dubbing
        resetDubbingUI();
    }
}

// Reset dubbing UI to initial state
function resetDubbingUI() {
    const startBtn = document.getElementById("startDubbing");
    const stopBtn = document.getElementById("stopDubbing");

    if (startBtn) {
        startBtn.innerHTML = '<span class="btn-icon">🎬</span><span class="btn-text">Start Dubbing</span>';
        startBtn.onclick = document.getElementById("startDubbing").onclick;
        UIUtils.toggleLoading(startBtn, false);
    }

    if (stopBtn) {
        stopBtn.style.display = "none";
    }

    StatusManager.update("Ready to start");
    currentDubbingId = null;
}

// Enhanced API key validation with better error handling
async function validateApiKeyAsync(apiKey) {
    try {
        const response = await fetch("https://api.elevenlabs.io/v1/user", {
            headers: { "xi-api-key": apiKey },
            timeout: 10000 // 10 second timeout
        });

        if (response.ok) {
            const userData = await response.json();
            // Store user info for future reference
            chrome.storage.local.set({ userInfo: userData });
            return true;
        }
        return false;
    } catch (error) {
        console.error("API key validation error:", error);
        return false;
    }
}

// Legacy callback version for compatibility
function validateApiKey(apiKey, callback) {
    validateApiKeyAsync(apiKey)
        .then(callback)
        .catch(() => callback(false));
}

// Enhanced message listener with better status handling
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.command) {
        case "updateStatus":
            StatusManager.update(message.status, message.progress);
            break;

        case "dubbingProgress":
            if (message.progress !== undefined) {
                StatusManager.update("Processing", message.progress);
            }
            break;

        case "dubbingComplete":
            StatusManager.update("Completed");
            currentDubbingId = null;
            resetDubbingUI();
            UIUtils.showNotification("Dubbing completed successfully!", "success");
            break;

        case "dubbingError":
            StatusManager.update("Error");
            resetDubbingUI();
            UIUtils.showNotification(message.error || "Dubbing failed", "error");
            break;

        case "showNotification":
            UIUtils.showNotification(message.message, message.type || "info");
            break;
    }

    // Send response to acknowledge message received
    sendResponse({ received: true });
});

// Keyboard shortcuts support
document.addEventListener("keydown", (event) => {
    // Ctrl/Cmd + D: Toggle dubbed audio
    if ((event.ctrlKey || event.metaKey) && event.key === "d") {
        event.preventDefault();
        const toggle = document.getElementById("dubbedAudioToggle");
        if (toggle) {
            toggle.click();
        }
    }

    // Ctrl/Cmd + R: Restart dubbing (when not in input field)
    if ((event.ctrlKey || event.metaKey) && event.key === "r" && !event.target.matches("input, textarea")) {
        event.preventDefault();
        const startBtn = document.getElementById("startDubbing");
        if (startBtn && !startBtn.classList.contains("disabled")) {
            startBtn.click();
        }
    }

    // Escape: Close modals
    if (event.key === "Escape") {
        const modal = document.querySelector(".modal[style*='flex']");
        if (modal) {
            modal.style.display = "none";
        }
    }
});

// Auto-save settings on change
function setupAutoSave() {
    const inputs = ["sourceLanguage", "targetLanguage", "numSpeakers", "audioQuality"];

    inputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener("change", () => {
                const settings = {};
                settings[inputId] = element.value;
                chrome.storage.local.set(settings);
            });
        }
    });
}

// Initialize auto-save when DOM is ready
if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", setupAutoSave);
} else {
    setupAutoSave();
}

// Performance monitoring
const PerformanceMonitor = {
    startTime: Date.now(),

    logTiming(event) {
        const elapsed = Date.now() - this.startTime;
        console.log(`[LingoCub] ${event}: ${elapsed}ms`);
    },

    trackUserAction(action) {
        // Could send analytics data here
        console.log(`[LingoCub] User action: ${action}`);
    }
};

// Track popup open time
PerformanceMonitor.logTiming("Popup opened");
