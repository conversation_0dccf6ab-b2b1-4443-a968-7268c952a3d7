{"manifest_version": 3, "name": "LingoCub - YouTube Video Dubbing", "version": "2.0.0", "description": "Real-time YouTube video dubbing with AI-powered voice translation. Supports 25+ languages with seamless audio synchronization.", "author": "LingoCub Team", "homepage_url": "https://github.com/lingocub/extension", "permissions": ["storage", "tabs", "activeTab", "scripting"], "host_permissions": ["*://*.youtube.com/*", "*://*.elevenlabs.io/*", "*://*.lingocub.vercel.app/*"], "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["*://*.youtube.com/*"], "js": ["content.js"], "run_at": "document_end", "all_frames": false}], "action": {"default_popup": "popup.html", "default_title": "LingoCub - YouTube Video Dubbing", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "commands": {"toggle-dubbing": {"suggested_key": {"default": "Ctrl+Shift+D", "mac": "Command+Shift+D"}, "description": "Toggle between dubbed and original audio"}, "restart-dubbing": {"suggested_key": {"default": "Ctrl+Shift+R", "mac": "Command+Shift+R"}, "description": "Restart dubbing process"}}, "web_accessible_resources": [{"resources": ["icons/*.png"], "matches": ["*://*.youtube.com/*"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; connect-src 'self' https://api.elevenlabs.io https://lingocub.vercel.app;"}, "minimum_chrome_version": "88"}