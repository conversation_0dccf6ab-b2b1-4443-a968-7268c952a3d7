// content.js - Enhanced YouTube Video Dubbing Extension

let isDubbedOn = true; // Default to dubbed audio on
let dubbedAudio; // Declare dubbedAudio globally to manage its state
let syncInterval; // For improved audio synchronization
let audioCache = new Map(); // Cache for audio files
let isVideoReady = false;
let lastSyncTime = 0;

// Enhanced audio synchronization settings
const SYNC_SETTINGS = {
    tolerance: 0.2, // Reduced tolerance for better sync
    checkInterval: 100, // Check sync every 100ms
    maxRetries: 3,
    fadeTime: 0.1
};

// Notification system for better user feedback
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotification = document.getElementById('lingocub-notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    const notification = document.createElement('div');
    notification.id = 'lingocub-notification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'error' ? '#ff4444' : type === 'success' ? '#44ff44' : '#4444ff'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-family: Arial, sans-serif;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease-out;
    `;

    // Add CSS animation
    if (!document.getElementById('lingocub-styles')) {
        const style = document.createElement('style');
        style.id = 'lingocub-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 4000);
}

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    const videoElement = document.querySelector("video");

    if (message.command === "showAlert") {
        showNotification(message.message, 'error');
    } else if (message.command === "playDubbedAudio") {
        if (!videoElement) {
            console.warn("No video element found on the page.");
            showNotification("No video element found on this page.", 'error');
            return;
        }

        handleDubbedAudioPlayback(message.audioUrl, videoElement);
    } else if (message.command === "toggleAudio") {
        toggleAudio(message.dubbed);
    }
});

// Enhanced audio playback handler
function handleDubbedAudioPlayback(audioUrl, videoElement) {
    // Clean up existing audio
    cleanupExistingAudio();

    showNotification("Loading dubbed audio...", 'info');

    // Check cache first
    if (audioCache.has(audioUrl)) {
        setupDubbedAudio(audioCache.get(audioUrl), videoElement);
        return;
    }

    // Fetch the audio file from the proxy URL with retry mechanism
    fetchAudioWithRetry(audioUrl, SYNC_SETTINGS.maxRetries)
        .then((blob) => {
            // Cache the audio blob
            audioCache.set(audioUrl, blob);
            setupDubbedAudio(blob, videoElement);
        })
        .catch((error) => {
            console.error("Error fetching dubbed audio in content script:", error);
            showNotification("Failed to load dubbed audio. Please try again.", 'error');
        });
}

// Fetch audio with retry mechanism
async function fetchAudioWithRetry(url, retries) {
    for (let i = 0; i <= retries; i++) {
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.blob();
        } catch (error) {
            if (i === retries) {
                throw error;
            }
            console.warn(`Fetch attempt ${i + 1} failed, retrying...`, error);
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1))); // Exponential backoff
        }
    }
}

// Setup dubbed audio with enhanced synchronization
function setupDubbedAudio(audioBlob, videoElement) {
    // Create a new audio element for the dubbed audio
    dubbedAudio = document.createElement("audio");
    dubbedAudio.id = "dubbedAudio";
    dubbedAudio.crossOrigin = "anonymous";
    dubbedAudio.style.display = "none";
    dubbedAudio.preload = "auto";
    document.body.appendChild(dubbedAudio);

    dubbedAudio.src = URL.createObjectURL(audioBlob);

    // Wait for audio to be ready
    dubbedAudio.addEventListener('loadeddata', () => {
        isVideoReady = true;
        setupAudioEventListeners(videoElement);
        showNotification("Dubbed audio ready!", 'success');

        // Initialize audio based on the saved toggle state
        chrome.storage.local.get("audioToggleState", (data) => {
            isDubbedOn = data.audioToggleState !== false;
            toggleAudio(isDubbedOn, false);
        });
    });

    dubbedAudio.addEventListener('error', (e) => {
        console.error("Audio loading error:", e);
        showNotification("Error loading dubbed audio.", 'error');
    });
}

// Enhanced audio event listeners setup
function setupAudioEventListeners(videoElement) {
    // Remove existing listeners to prevent duplicates
    removeAudioEventListeners(videoElement);

    // Enhanced sync function with better performance
    const syncAudio = () => {
        if (!dubbedAudio || !isVideoReady) return;

        const now = Date.now();
        if (now - lastSyncTime < SYNC_SETTINGS.checkInterval) return;
        lastSyncTime = now;

        const offset = videoElement.currentTime - dubbedAudio.currentTime;
        if (Math.abs(offset) > SYNC_SETTINGS.tolerance) {
            dubbedAudio.currentTime = videoElement.currentTime;
        }
    };

    // Start continuous sync monitoring
    if (syncInterval) clearInterval(syncInterval);
    syncInterval = setInterval(syncAudio, SYNC_SETTINGS.checkInterval);

    // Video event listeners
    videoElement.addEventListener("play", handleVideoPlay);
    videoElement.addEventListener("pause", handleVideoPause);
    videoElement.addEventListener("timeupdate", syncAudio);
    videoElement.addEventListener("ratechange", handleRateChange);
    videoElement.addEventListener("seeked", handleVideoSeeked);
    videoElement.addEventListener("volumechange", handleVolumeChange);
}

// Event handler functions
function handleVideoPlay() {
    if (isDubbedOn && dubbedAudio && isVideoReady) {
        dubbedAudio.play().catch((error) => {
            console.error("Error playing dubbed audio on video play:", error);
            showNotification("Error playing dubbed audio", 'error');
        });
    }
}

function handleVideoPause() {
    if (dubbedAudio) {
        dubbedAudio.pause();
    }
}

function handleRateChange() {
    if (dubbedAudio) {
        dubbedAudio.playbackRate = document.querySelector("video").playbackRate;
    }
}

function handleVideoSeeked() {
    const videoElement = document.querySelector("video");
    if (dubbedAudio && isVideoReady) {
        dubbedAudio.currentTime = videoElement.currentTime;
        if (isDubbedOn && !videoElement.paused) {
            dubbedAudio.play().catch(console.error);
        } else {
            dubbedAudio.pause();
        }
    }
}

function handleVolumeChange() {
    const videoElement = document.querySelector("video");
    if (isDubbedOn && videoElement.volume > 0) {
        // Ensure original video stays muted when dubbed audio is on
        videoElement.volume = 0;
    }
}

// Remove event listeners to prevent memory leaks
function removeAudioEventListeners(videoElement) {
    if (videoElement) {
        videoElement.removeEventListener("play", handleVideoPlay);
        videoElement.removeEventListener("pause", handleVideoPause);
        videoElement.removeEventListener("ratechange", handleRateChange);
        videoElement.removeEventListener("seeked", handleVideoSeeked);
        videoElement.removeEventListener("volumechange", handleVolumeChange);
    }
    if (syncInterval) {
        clearInterval(syncInterval);
        syncInterval = null;
    }
}

// Clean up existing audio resources
function cleanupExistingAudio() {
    if (dubbedAudio) {
        dubbedAudio.pause();
        dubbedAudio.src = "";
        dubbedAudio.load();
        if (dubbedAudio.parentNode) {
            dubbedAudio.remove();
        }
        dubbedAudio = null;
    }
    isVideoReady = false;
    removeAudioEventListeners(document.querySelector("video"));
}

// Enhanced toggle function with smooth transitions
function toggleAudio(isDubbed, playImmediately = true) {
    const videoElement = document.querySelector("video");
    if (!videoElement) return;

    isDubbedOn = isDubbed;

    if (isDubbed) {
        // Smooth transition to dubbed audio
        if (videoElement.volume > 0) {
            fadeOutOriginalAudio(videoElement);
        }

        if (dubbedAudio && playImmediately && !videoElement.paused && isVideoReady) {
            dubbedAudio.currentTime = videoElement.currentTime;
            dubbedAudio.play().catch(console.error);
        }

        showNotification("Switched to dubbed audio", 'success');
    } else {
        // Switch back to original audio
        if (dubbedAudio) {
            dubbedAudio.pause();
        }
        fadeInOriginalAudio(videoElement);
        showNotification("Switched to original audio", 'success');
    }
}

// Smooth audio transitions
function fadeOutOriginalAudio(videoElement) {
    const fadeSteps = 10;
    const stepTime = SYNC_SETTINGS.fadeTime * 1000 / fadeSteps;
    let currentStep = 0;

    const fadeInterval = setInterval(() => {
        currentStep++;
        videoElement.volume = Math.max(0, 1 - (currentStep / fadeSteps));

        if (currentStep >= fadeSteps) {
            clearInterval(fadeInterval);
            videoElement.volume = 0;
        }
    }, stepTime);
}

function fadeInOriginalAudio(videoElement) {
    const fadeSteps = 10;
    const stepTime = SYNC_SETTINGS.fadeTime * 1000 / fadeSteps;
    let currentStep = 0;

    const fadeInterval = setInterval(() => {
        currentStep++;
        videoElement.volume = Math.min(1, currentStep / fadeSteps);

        if (currentStep >= fadeSteps) {
            clearInterval(fadeInterval);
            videoElement.volume = 1;
        }
    }, stepTime);
}

// Cleanup when page unloads
window.addEventListener('beforeunload', () => {
    cleanupExistingAudio();
    // Clear cache to free memory
    audioCache.clear();
});
