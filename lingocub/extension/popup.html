<!DOCTYPE html>
<html>
<head>
  <title>LingoCub</title>
  <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
  <div id="container">
    <h2>Welcome to LingoCub!</h2>

    <label for="apiKey">Enter your ElevenLabs API Key:</label>
    <input type="password" id="apiKey" placeholder="Your API Key" />

    <label for="sourceLanguage">Select Source Language:</label>
        <select id="sourceLanguage">
            <option value="auto">Auto Detect</option>
            <option value="en">English</option>
            <option value="hi">Hindi</option>
            <option value="pt">Portuguese</option>
            <option value="zh">Chinese</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
            <option value="ja">Japanese</option>
            <option value="ar">Arabic</option>
            <option value="ru">Russian</option>
            <option value="ko">Korean</option>
            <option value="id">Indonesian</option>
            <option value="it">Italian</option>
            <option value="nl">Dutch</option>
            <option value="tr">Turkish</option>
            <option value="pl">Polish</option>
            <option value="sv">Swedish</option>
            <option value="fil">Filipino</option>
            <option value="ms">Malay</option>
            <option value="ro">Romanian</option>
            <option value="uk">Ukrainian</option>
            <option value="el">Greek</option>
            <option value="cs">Czech</option>
            <option value="da">Danish</option>
            <option value="fi">Finnish</option>
            <option value="bg">Bulgarian</option>
            <option value="hr">Croatian</option>
            <option value="sk">Slovak</option>
            <option value="ta">Tamil</option>
        </select>

    <label for="targetLanguage">Select Target Language:</label>
        <select id="targetLanguage">
            <option value="en">English</option>
            <option value="hi">Hindi</option>
            <option value="pt">Portuguese</option>
            <option value="zh">Chinese</option>
            <option value="es">Spanish</option>
            <option value="fr">French</option>
            <option value="de">German</option>
            <option value="ja">Japanese</option>
            <option value="ar">Arabic</option>
            <option value="ru">Russian</option>
            <option value="ko">Korean</option>
            <option value="id">Indonesian</option>
            <option value="it">Italian</option>
            <option value="nl">Dutch</option>
            <option value="tr">Turkish</option>
            <option value="pl">Polish</option>
            <option value="sv">Swedish</option>
            <option value="fil">Filipino</option>
            <option value="ms">Malay</option>
            <option value="ro">Romanian</option>
            <option value="uk">Ukrainian</option>
            <option value="el">Greek</option>
            <option value="cs">Czech</option>
            <option value="da">Danish</option>
            <option value="fi">Finnish</option>
            <option value="bg">Bulgarian</option>
            <option value="hr">Croatian</option>
            <option value="sk">Slovak</option>
            <option value="ta">Tamil</option>
        </select>

    <label for="numSpeakers">Number of Speakers:</label>
    <select id="numSpeakers">
        <option value="0">Auto Detect</option>
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
        <option value="4">4</option>
        <option value="5">5</option>
        <option value="6">6</option>
        <option value="7">7</option>
        <option value="8">8</option>
        <option value="9">9</option>
    </select>

    <button id="startDubbing">Start Dubbing</button>

    <div id="status-container">
        <span id="status-text" class="status-default">Status:</span>
      </div>

    <div id="dubbedAudioToggle-container">
      <input type="checkbox" id="dubbedAudioToggle" class="custom-checkbox" />
      <label for="dubbedAudioToggle" class="toggle-switch"></label>
      <label for="dubbedAudioToggle" class="toggle-label">Enable Dubbed Audio</label>
    </div>
      
    
        <!-- Disclaimer/Instructions Section -->
    <div id="disclaimer">
      <p><strong>Note:</strong> If you want to stop the dubbing audio completely, please refresh the YouTube page. Navigating to a new video on the same page will not automatically stop the dubbing audio. <br/><br/> If you want to replace the current dubbed audio, select your desired target language and click the 'Start Dubbing' button.</p>
    </div>
  </div>


  <script src="popup.js"></script>
</body>
</html>
