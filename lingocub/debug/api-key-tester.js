// API Key Diagnostic Tool for LingoCub
// This tool helps diagnose API key validation issues

class APIKeyDiagnostic {
    constructor() {
        this.apiKey = null;
        this.testResults = [];
    }

    // Main diagnostic function
    async diagnose(apiKey) {
        this.apiKey = apiKey;
        console.log('🔍 Starting API Key Diagnostic...\n');
        
        await this.testAPIKeyFormat();
        await this.testNetworkConnectivity();
        await this.testElevenLabsAPI();
        await this.testAPIKeyValidation();
        await this.testAPIQuota();
        
        this.displayResults();
        this.provideRecommendations();
    }

    // Test 1: API Key Format
    async testAPIKeyFormat() {
        const testName = 'API Key Format';
        
        if (!this.apiKey) {
            this.addResult(testName, false, 'No API key provided');
            return;
        }

        // Basic format checks
        const trimmedKey = this.apiKey.trim();
        const hasValidLength = trimmedKey.length >= 10;
        const hasValidChars = /^[a-zA-Z0-9_-]+$/.test(trimmedKey);
        
        if (hasValidLength && hasValidChars) {
            this.addResult(testName, true, `Key format appears valid (${trimmedKey.length} chars)`);
        } else {
            const issues = [];
            if (!hasValidLength) issues.push('too short');
            if (!hasValidChars) issues.push('invalid characters');
            this.addResult(testName, false, `Format issues: ${issues.join(', ')}`);
        }
    }

    // Test 2: Network Connectivity
    async testNetworkConnectivity() {
        const testName = 'Network Connectivity';
        
        try {
            const response = await fetch('https://api.elevenlabs.io', {
                method: 'HEAD',
                mode: 'no-cors'
            });
            this.addResult(testName, true, 'Can reach ElevenLabs API endpoint');
        } catch (error) {
            this.addResult(testName, false, `Network error: ${error.message}`);
        }
    }

    // Test 3: ElevenLabs API Availability
    async testElevenLabsAPI() {
        const testName = 'ElevenLabs API Availability';
        
        try {
            const response = await fetch('https://api.elevenlabs.io/v1/user', {
                method: 'GET',
                headers: {
                    'xi-api-key': 'test-key' // This will fail but tells us if API is responding
                }
            });
            
            // We expect a 401 (unauthorized) which means API is working
            if (response.status === 401) {
                this.addResult(testName, true, 'ElevenLabs API is responding correctly');
            } else {
                this.addResult(testName, false, `Unexpected response: ${response.status}`);
            }
        } catch (error) {
            this.addResult(testName, false, `API unavailable: ${error.message}`);
        }
    }

    // Test 4: API Key Validation
    async testAPIKeyValidation() {
        const testName = 'API Key Validation';
        
        if (!this.apiKey) {
            this.addResult(testName, false, 'No API key to validate');
            return;
        }

        try {
            const response = await fetch('https://api.elevenlabs.io/v1/user', {
                method: 'GET',
                headers: {
                    'xi-api-key': this.apiKey.trim()
                }
            });

            if (response.ok) {
                const userData = await response.json();
                this.addResult(testName, true, `Valid API key for user: ${userData.subscription?.tier || 'Unknown'}`);
                return userData;
            } else {
                const errorText = await response.text();
                let errorMsg = `HTTP ${response.status}`;
                
                try {
                    const errorData = JSON.parse(errorText);
                    errorMsg += `: ${errorData.detail || errorData.message || errorText}`;
                } catch {
                    errorMsg += `: ${errorText}`;
                }
                
                this.addResult(testName, false, errorMsg);
            }
        } catch (error) {
            this.addResult(testName, false, `Validation error: ${error.message}`);
        }
        
        return null;
    }

    // Test 5: API Quota Check
    async testAPIQuota() {
        const testName = 'API Quota Check';
        
        if (!this.apiKey) {
            this.addResult(testName, false, 'No API key to check quota');
            return;
        }

        try {
            const response = await fetch('https://api.elevenlabs.io/v1/user/subscription', {
                method: 'GET',
                headers: {
                    'xi-api-key': this.apiKey.trim()
                }
            });

            if (response.ok) {
                const subscriptionData = await response.json();
                const characterCount = subscriptionData.character_count || 0;
                const characterLimit = subscriptionData.character_limit || 0;
                const remaining = characterLimit - characterCount;
                
                if (remaining > 0) {
                    this.addResult(testName, true, `${remaining} characters remaining`);
                } else {
                    this.addResult(testName, false, 'No characters remaining in quota');
                }
            } else {
                this.addResult(testName, false, `Cannot check quota: HTTP ${response.status}`);
            }
        } catch (error) {
            this.addResult(testName, false, `Quota check error: ${error.message}`);
        }
    }

    // Add test result
    addResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message
        });
    }

    // Display results
    displayResults() {
        console.log('\n📊 Diagnostic Results:');
        console.log('=====================\n');
        
        this.testResults.forEach(result => {
            const status = result.passed ? '✅' : '❌';
            console.log(`${status} ${result.name}: ${result.message}`);
        });
    }

    // Provide recommendations
    provideRecommendations() {
        console.log('\n💡 Recommendations:');
        console.log('==================\n');
        
        const failedTests = this.testResults.filter(r => !r.passed);
        
        if (failedTests.length === 0) {
            console.log('✅ All tests passed! Your API key should work correctly.');
            return;
        }

        failedTests.forEach(test => {
            switch (test.name) {
                case 'API Key Format':
                    console.log('🔑 API Key Format Issues:');
                    console.log('   • Check that you copied the complete API key');
                    console.log('   • Remove any extra spaces or characters');
                    console.log('   • Get a new API key from https://elevenlabs.io/app/speech-synthesis');
                    break;
                    
                case 'Network Connectivity':
                    console.log('🌐 Network Issues:');
                    console.log('   • Check your internet connection');
                    console.log('   • Disable VPN if using one');
                    console.log('   • Check if firewall is blocking requests');
                    break;
                    
                case 'ElevenLabs API Availability':
                    console.log('🔧 API Availability Issues:');
                    console.log('   • ElevenLabs API might be down');
                    console.log('   • Check https://status.elevenlabs.io for service status');
                    console.log('   • Try again in a few minutes');
                    break;
                    
                case 'API Key Validation':
                    console.log('🚫 API Key Validation Issues:');
                    console.log('   • Your API key is invalid or expired');
                    console.log('   • Log into ElevenLabs and generate a new API key');
                    console.log('   • Make sure your account is active');
                    break;
                    
                case 'API Quota Check':
                    console.log('📊 Quota Issues:');
                    console.log('   • You have exceeded your character limit');
                    console.log('   • Upgrade your ElevenLabs subscription');
                    console.log('   • Wait for quota reset (monthly)');
                    break;
            }
            console.log('');
        });

        console.log('🔗 Helpful Links:');
        console.log('   • ElevenLabs Dashboard: https://elevenlabs.io/app');
        console.log('   • API Documentation: https://docs.elevenlabs.io');
        console.log('   • Support: https://help.elevenlabs.io');
    }

    // Quick fix suggestions
    getQuickFixes() {
        const fixes = [];
        
        const failedTests = this.testResults.filter(r => !r.passed);
        
        failedTests.forEach(test => {
            switch (test.name) {
                case 'API Key Format':
                    fixes.push('Re-copy your API key from ElevenLabs dashboard');
                    break;
                case 'API Key Validation':
                    fixes.push('Generate a new API key from ElevenLabs');
                    break;
                case 'API Quota Check':
                    fixes.push('Upgrade your ElevenLabs subscription');
                    break;
            }
        });
        
        return fixes;
    }
}

// Usage function for the extension
async function diagnoseAPIKey(apiKey) {
    const diagnostic = new APIKeyDiagnostic();
    await diagnostic.diagnose(apiKey);
    return diagnostic.getQuickFixes();
}

// Auto-run if API key is provided
if (typeof window !== 'undefined') {
    window.diagnoseAPIKey = diagnoseAPIKey;
    window.APIKeyDiagnostic = APIKeyDiagnostic;
}

// Export for Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APIKeyDiagnostic, diagnoseAPIKey };
}
