// Quick Diagnostic Script for LingoCub
// Run this in the browser console to diagnose common issues

(function() {
    'use strict';
    
    console.log('🔍 LingoCub Quick Diagnostic Tool');
    console.log('==================================\n');
    
    // Check if we're in the right context
    if (typeof chrome === 'undefined' || !chrome.runtime) {
        console.log('❌ This script must be run in a Chrome extension context');
        console.log('💡 Please run this in the extension popup or on a YouTube page with the extension loaded');
        return;
    }
    
    async function runDiagnostic() {
        const results = {
            extension: await checkExtension(),
            storage: await checkStorage(),
            youtube: checkYouTubePage(),
            network: await checkNetwork(),
            apiKey: await checkApiKey()
        };
        
        displayResults(results);
        provideRecommendations(results);
    }
    
    // Check extension status
    async function checkExtension() {
        try {
            const manifest = chrome.runtime.getManifest();
            return {
                loaded: true,
                version: manifest.version,
                name: manifest.name,
                permissions: manifest.permissions || []
            };
        } catch (error) {
            return {
                loaded: false,
                error: error.message
            };
        }
    }
    
    // Check storage
    async function checkStorage() {
        try {
            const data = await new Promise((resolve) => {
                chrome.storage.local.get(null, resolve);
            });
            
            return {
                accessible: true,
                hasApiKey: !!data.apiKey,
                apiKeyLength: data.apiKey ? data.apiKey.length : 0,
                hasSettings: !!(data.sourceLanguage && data.targetLanguage),
                lastValidation: data.lastValidation,
                userInfo: data.userInfo
            };
        } catch (error) {
            return {
                accessible: false,
                error: error.message
            };
        }
    }
    
    // Check if on YouTube
    function checkYouTubePage() {
        const url = window.location.href;
        const isYouTube = url.includes('youtube.com');
        const isVideoPage = url.includes('/watch?v=');
        const hasVideoElement = !!document.querySelector('video');
        
        return {
            isYouTube,
            isVideoPage,
            hasVideoElement,
            url: url
        };
    }
    
    // Check network connectivity
    async function checkNetwork() {
        const tests = [
            { name: 'ElevenLabs API', url: 'https://api.elevenlabs.io' },
            { name: 'Proxy Server', url: 'https://lingocub.vercel.app' }
        ];
        
        const results = {};
        
        for (const test of tests) {
            try {
                const response = await fetch(test.url, { 
                    method: 'HEAD', 
                    mode: 'no-cors',
                    signal: AbortSignal.timeout(5000)
                });
                results[test.name] = { accessible: true };
            } catch (error) {
                results[test.name] = { 
                    accessible: false, 
                    error: error.message 
                };
            }
        }
        
        return results;
    }
    
    // Check API key
    async function checkApiKey() {
        try {
            const data = await new Promise((resolve) => {
                chrome.storage.local.get(['apiKey'], resolve);
            });
            
            if (!data.apiKey) {
                return { present: false };
            }
            
            const apiKey = data.apiKey.trim();
            
            // Test API key
            try {
                const response = await fetch('https://api.elevenlabs.io/v1/user', {
                    headers: { 'xi-api-key': apiKey },
                    signal: AbortSignal.timeout(10000)
                });
                
                if (response.ok) {
                    const userData = await response.json();
                    return {
                        present: true,
                        valid: true,
                        user: userData.subscription?.tier || 'Unknown',
                        characterCount: userData.subscription?.character_count,
                        characterLimit: userData.subscription?.character_limit
                    };
                } else {
                    return {
                        present: true,
                        valid: false,
                        error: `HTTP ${response.status}`,
                        status: response.status
                    };
                }
            } catch (error) {
                return {
                    present: true,
                    valid: false,
                    error: error.message
                };
            }
        } catch (error) {
            return {
                present: false,
                error: error.message
            };
        }
    }
    
    // Display results
    function displayResults(results) {
        console.log('📊 Diagnostic Results:');
        console.log('=====================\n');
        
        // Extension status
        console.log('🔧 Extension Status:');
        if (results.extension.loaded) {
            console.log(`   ✅ Loaded: ${results.extension.name} v${results.extension.version}`);
            console.log(`   📋 Permissions: ${results.extension.permissions.join(', ')}`);
        } else {
            console.log(`   ❌ Not loaded: ${results.extension.error}`);
        }
        console.log('');
        
        // Storage status
        console.log('💾 Storage Status:');
        if (results.storage.accessible) {
            console.log(`   ✅ Accessible`);
            console.log(`   🔑 API Key: ${results.storage.hasApiKey ? `Present (${results.storage.apiKeyLength} chars)` : 'Missing'}`);
            console.log(`   ⚙️  Settings: ${results.storage.hasSettings ? 'Configured' : 'Not configured'}`);
            if (results.storage.lastValidation) {
                const lastValidation = new Date(results.storage.lastValidation);
                console.log(`   🕒 Last validation: ${lastValidation.toLocaleString()}`);
            }
        } else {
            console.log(`   ❌ Not accessible: ${results.storage.error}`);
        }
        console.log('');
        
        // YouTube status
        console.log('🎬 YouTube Status:');
        console.log(`   🌐 On YouTube: ${results.youtube.isYouTube ? '✅' : '❌'}`);
        console.log(`   📺 Video page: ${results.youtube.isVideoPage ? '✅' : '❌'}`);
        console.log(`   🎵 Video element: ${results.youtube.hasVideoElement ? '✅' : '❌'}`);
        console.log('');
        
        // Network status
        console.log('🌐 Network Status:');
        Object.entries(results.network).forEach(([name, status]) => {
            const icon = status.accessible ? '✅' : '❌';
            const message = status.accessible ? 'Accessible' : status.error;
            console.log(`   ${icon} ${name}: ${message}`);
        });
        console.log('');
        
        // API Key status
        console.log('🔑 API Key Status:');
        if (results.apiKey.present) {
            if (results.apiKey.valid) {
                console.log(`   ✅ Valid API key`);
                console.log(`   👤 User tier: ${results.apiKey.user}`);
                if (results.apiKey.characterCount !== undefined) {
                    const remaining = results.apiKey.characterLimit - results.apiKey.characterCount;
                    console.log(`   📊 Characters: ${remaining}/${results.apiKey.characterLimit} remaining`);
                }
            } else {
                console.log(`   ❌ Invalid API key: ${results.apiKey.error}`);
            }
        } else {
            console.log(`   ❌ No API key found`);
        }
        console.log('');
    }
    
    // Provide recommendations
    function provideRecommendations(results) {
        console.log('💡 Recommendations:');
        console.log('==================\n');
        
        const issues = [];
        
        if (!results.extension.loaded) {
            issues.push('Extension not loaded properly');
        }
        
        if (!results.storage.accessible) {
            issues.push('Storage not accessible');
        }
        
        if (!results.storage.hasApiKey) {
            issues.push('No API key configured');
        }
        
        if (results.apiKey.present && !results.apiKey.valid) {
            issues.push('Invalid API key');
        }
        
        if (!results.youtube.isYouTube) {
            issues.push('Not on YouTube');
        }
        
        if (!results.youtube.isVideoPage) {
            issues.push('Not on a YouTube video page');
        }
        
        Object.entries(results.network).forEach(([name, status]) => {
            if (!status.accessible) {
                issues.push(`Cannot reach ${name}`);
            }
        });
        
        if (issues.length === 0) {
            console.log('✅ No issues detected! Extension should work properly.');
        } else {
            console.log('⚠️  Issues detected:');
            issues.forEach(issue => console.log(`   • ${issue}`));
            
            console.log('\n🔧 Suggested fixes:');
            
            if (!results.storage.hasApiKey || (results.apiKey.present && !results.apiKey.valid)) {
                console.log('   1. Get a new API key from https://elevenlabs.io/app/speech-synthesis');
                console.log('   2. Enter the API key in the extension popup');
                console.log('   3. Use the "Test Key" button to verify');
            }
            
            if (!results.youtube.isVideoPage) {
                console.log('   1. Navigate to a YouTube video page');
                console.log('   2. Make sure the URL contains "/watch?v="');
            }
            
            if (!results.extension.loaded) {
                console.log('   1. Reload the extension in chrome://extensions/');
                console.log('   2. Make sure developer mode is enabled');
            }
        }
        
        console.log('\n📞 Need more help?');
        console.log('   • Check the troubleshooting guide');
        console.log('   • Submit an issue on GitHub');
        console.log('   • Run this diagnostic again after making changes');
    }
    
    // Run the diagnostic
    runDiagnostic().catch(error => {
        console.error('❌ Diagnostic failed:', error);
    });
    
})();

// Also provide a simple function for manual testing
window.testLingoCub = async function() {
    console.log('🧪 Testing LingoCub functionality...');
    
    try {
        const data = await new Promise((resolve) => {
            chrome.storage.local.get(['apiKey'], resolve);
        });
        
        if (!data.apiKey) {
            console.log('❌ No API key found. Please configure your API key first.');
            return;
        }
        
        const response = await fetch('https://api.elevenlabs.io/v1/user', {
            headers: { 'xi-api-key': data.apiKey }
        });
        
        if (response.ok) {
            console.log('✅ API key is working correctly!');
        } else {
            console.log(`❌ API key test failed: HTTP ${response.status}`);
        }
    } catch (error) {
        console.log(`❌ Test failed: ${error.message}`);
    }
};
