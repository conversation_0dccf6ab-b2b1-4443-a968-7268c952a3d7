<div align="center">
  <img src="/docs/images/logo.jpg" alt="KlicStudio" height="90">

  # أداة ترجمة الفيديو والصوت باستخدام الذكاء الاصطناعي

  <a href="https://trendshift.io/repositories/13360" target="_blank"><img src="https://trendshift.io/api/badge/repositories/13360" alt="KrillinAI%2FKlicStudio | Trendshift" style="width: 250px; height: 55px;" width="250" height="55"/></a>

  **[English](/README.md)｜[简体中文](/docs/zh/README.md)｜[日本語](/docs/jp/README.md)｜[한국어](/docs/kr/README.md)｜[Tiếng Việt](/docs/vi/README.md)｜[Français](/docs/fr/README.md)｜[Deutsch](/docs/de/README.md)｜[Español](/docs/es/README.md)｜[Português](/docs/pt/README.md)｜[Русский](/docs/rus/README.md)｜[اللغة العربية](/docs/ar/README.md)**

[![Twitter](https://img.shields.io/badge/Twitter-KrillinAI-orange?logo=twitter)](https://x.com/KrillinAI)
[![QQ 群](https://img.shields.io/badge/QQ%20群-754069680-green?logo=tencent-qq)](https://jq.qq.com/?_wv=1027&k=754069680)
[![Bilibili](https://img.shields.io/badge/dynamic/json?label=Bilibili&query=%24.data.follower&suffix=粉丝&url=https%3A%2F%2Fapi.bilibili.com%2Fx%2Frelation%2Fstat%3Fvmid%3D242124650&logo=bilibili&color=00A1D6&labelColor=FE7398&logoColor=FFFFFF)](https://space.bilibili.com/242124650)

</div>

 ## مقدمة المشروع  ([جرب النسخة عبر الإنترنت الآن!](https://www.klic.studio/))

Klic Studio هو حل شامل لتوطين وتعزيز الصوت والفيديو تم تطويره بواسطة Krillin AI. هذه الأداة البسيطة والقوية تجمع بين ترجمة الفيديو والصوت، وتكرار الصوت، واستنساخ الصوت، وتدعم إخراج بتنسيقات أفقية ورأسية، مما يضمن عرضها بشكل مثالي على جميع المنصات الرئيسية (Bilibili، Xiaohongshu، Douyin، WeChat Video، Kuaishou، YouTube، TikTok، إلخ). من خلال سير العمل من البداية إلى النهاية، يمكنك تحويل المواد الأصلية إلى محتوى متعدد المنصات جاهز للاستخدام بنقرات قليلة فقط.

## الميزات والوظائف الرئيسية:
🎯 **تشغيل بنقرة واحدة**: لا حاجة لتكوين بيئة معقدة، تثبيت تلقائي للاعتماديات، ابدأ الاستخدام على الفور، إصدار سطح مكتب جديد، استخدام أكثر سهولة!

📥 **الحصول على الفيديو**: يدعم تنزيل yt-dlp أو تحميل الملفات المحلية

📜 **التعرف الدقيق**: التعرف على الصوت بدقة عالية بناءً على Whisper

🧠 **التقسيم الذكي**: استخدام LLM لتقسيم وتنسيق الترجمة

🔄 **استبدال المصطلحات**: استبدال المصطلحات المتخصصة بنقرة واحدة 

🌍 **ترجمة احترافية**: ترجمة LLM مع الحفاظ على المعنى الطبيعي

🎙️ **استنساخ الصوت**: تقديم أصوات مختارة من CosyVoice أو استنساخ صوت مخصص

🎬 **دمج الفيديو**: معالجة تلقائية للفيديوهات الأفقية والرأسية وتنسيق الترجمة

💻 **عبر المنصات**: يدعم Windows وLinux وmacOS، يوفر إصدار سطح مكتب وإصدار خادم


## عرض النتائج
الصورة أدناه توضح تأثير ملف الترجمة الناتج بعد استيراد فيديو محلي مدته 46 دقيقة وتنفيذه بنقرة واحدة، دون أي تعديلات يدوية. لا توجد أي فقدان أو تداخل، والفواصل طبيعية، وجودة الترجمة عالية جدًا.
![تأثير المحاذاة](/docs/images/alignment.png)

<table>
<tr>
<td width="33%">

### ترجمة الترجمة
---
https://github.com/user-attachments/assets/bba1ac0a-fe6b-4947-b58d-ba99306d0339

</td>
<td width="33%">



### الصوت
---
https://github.com/user-attachments/assets/0b32fad3-c3ad-4b6a-abf0-0865f0dd2385

</td>

<td width="33%">

### عمودي
---
https://github.com/user-attachments/assets/c2c7b528-0ef8-4ba9-b8ac-f9f92f6d4e71

</td>

</tr>
</table>

## 🔍 دعم خدمات التعرف على الصوت
_**جميع النماذج المحلية في الجدول أدناه تدعم التثبيت التلقائي للملفات القابلة للتنفيذ + ملفات النموذج، كل ما عليك هو الاختيار، والباقي ستقوم Klic بإعداده لك.**_

| مصدر الخدمة          | المنصات المدعومة      | خيارات النموذج                             | محلي/سحابي | ملاحظات         |
|--------------------|-----------------|----------------------------------------|-------|-------------|
| **OpenAI Whisper** | جميع المنصات       | -                                      | سحابي    | سريع وفعال      |
| **FasterWhisper**  | Windows/Linux   | `tiny`/`medium`/`large-v2` (موصى به medium+) | محلي    | أسرع، بدون تكاليف سحابية |
| **WhisperKit**     | macOS (لرقائق M فقط) | `large-v2`                             | محلي    | تحسين أصلي لرقائق Apple |
| **WhisperCpp**     | جميع المنصات       | `large-v2`                             | محلي    | يدعم جميع المنصات       |
| **Alibaba Cloud ASR** | جميع المنصات       | -                                      | سحابي    | لتجنب مشاكل الشبكة في البر الرئيسي للصين  |

## 🚀 دعم نماذج اللغة الكبيرة

✅ متوافق مع جميع خدمات نماذج اللغة الكبيرة السحابية/المحلية التي تتوافق مع **معايير OpenAI API**، بما في ذلك على سبيل المثال لا الحصر:
- OpenAI
- Gemini
- DeepSeek
- Tongyi Qianwen
- نماذج مفتوحة المصدر المثبتة محليًا
- خدمات API الأخرى المتوافقة مع تنسيق OpenAI

## 🎤 دعم تحويل النص إلى صوت (TTS)
- خدمة صوتية من Alibaba Cloud
- OpenAI TTS

## دعم اللغات
اللغات المدخلة المدعومة: الصينية، الإنجليزية، اليابانية، الألمانية، التركية، الكورية، الروسية، الماليزية (تستمر في الزيادة)

اللغات المدعومة للترجمة: الإنجليزية، الصينية، الروسية، الإسبانية، الفرنسية، وغيرها من 101 لغة

## معاينة الواجهة
![معاينة الواجهة](/docs/images/ui_desktop.png)


## 🚀 البدء السريع
### الخطوات الأساسية
أولاً، قم بتنزيل [الإصدار](https://github.com/KrillinAI/KlicStudio/releases) الذي يتناسب مع نظام جهازك، ثم اختر بين إصدار سطح المكتب أو غير سطح المكتب وفقًا للدليل أدناه، ثم ضع الملفات في مجلد فارغ، قم بتنزيل البرنامج إلى مجلد فارغ، لأنه بعد التشغيل سيتم إنشاء بعض الدلائل، وضعها في مجلد فارغ سيسهل إدارتها.  

【إذا كان إصدار سطح المكتب، أي ملف الإصدار الذي يحمل كلمة desktop انظر هنا】  
_إصدار سطح المكتب هو إصدار جديد، تم إصداره لحل مشكلة صعوبة تحرير ملفات التكوين بشكل صحيح من قبل المستخدمين الجدد، وهناك بعض الأخطاء، يتم تحديثه باستمرار_
1. انقر نقرًا مزدوجًا على الملف لبدء الاستخدام (يحتاج إصدار سطح المكتب أيضًا إلى تكوين، يتم تكوينه داخل البرنامج)

【إذا كان إصدار غير سطح المكتب، أي ملف الإصدار الذي لا يحمل كلمة desktop انظر هنا】  
_إصدار غير سطح المكتب هو الإصدار الأول، التكوين أكثر تعقيدًا، ولكنه مستقر، كما أنه مناسب للنشر على الخادم، لأنه سيقدم واجهة مستخدم عبر الويب_
1. في المجلد، أنشئ مجلدًا باسم `config`، ثم في مجلد `config` أنشئ ملفًا باسم `config.toml`، انسخ محتوى ملف `config-example.toml` الموجود في دليل `config` واملأه في `config.toml`، واملأ معلومات التكوين الخاصة بك وفقًا للتعليقات.
2. انقر نقرًا مزدوجًا، أو نفذ الملف القابل للتنفيذ في الطرفية، لبدء الخدمة 
3. افتح المتصفح، أدخل `http://127.0.0.1:8888`، وابدأ الاستخدام (استبدل 8888 بالمنفذ الذي قمت بملئه في ملف التكوين)

### إلى: مستخدمي macOS
【إذا كان إصدار سطح المكتب، أي ملف الإصدار الذي يحمل كلمة desktop انظر هنا】  
حاليًا، بسبب مشاكل في التوقيع، لا يمكن حزم إصدار سطح المكتب ليعمل بنقرة مزدوجة مباشرة أو تثبيت dmg، تحتاج إلى الوثوق بالتطبيق يدويًا، الطريقة كالتالي:
1. افتح الملف القابل للتنفيذ في الطرفية (افترض أن اسم الملف هو KlicStudio_1.0.0_desktop_macOS_arm64) في الدليل
2. نفذ الأوامر التالية بالتتابع:
```
sudo xattr -cr ./KlicStudio_1.0.0_desktop_macOS_arm64
sudo chmod +x ./KlicStudio_1.0.0_desktop_macOS_arm64 
./KlicStudio_1.0.0_desktop_macOS_arm64
```

【إذا كان إصدار غير سطح المكتب، أي ملف الإصدار الذي لا يحمل كلمة desktop انظر هنا】  
لم يتم توقيع هذا البرنامج، لذلك عند تشغيله على macOS، بعد إكمال تكوين الملفات في "الخطوات الأساسية"، تحتاج أيضًا إلى الوثوق بالتطبيق يدويًا، الطريقة كالتالي:
1. افتح الملف القابل للتنفيذ في الطرفية (افترض أن اسم الملف هو KlicStudio_1.0.0_macOS_arm64) في الدليل
2. نفذ الأوامر التالية بالتتابع:
   ```
    sudo xattr -rd com.apple.quarantine ./KlicStudio_1.0.0_macOS_arm64
    sudo chmod +x ./KlicStudio_1.0.0_macOS_arm64
    ./KlicStudio_1.0.0_macOS_arm64
    ```
    لبدء الخدمة

### نشر Docker
يدعم هذا المشروع نشر Docker، يرجى الرجوع إلى [إرشادات نشر Docker](./docker.md)

### إرشادات تكوين الكوكيز (غير إلزامية)

إذا واجهت مشكلة في تنزيل الفيديو

يرجى الرجوع إلى [إرشادات تكوين الكوكيز](./get_cookies.md) لتكوين معلومات الكوكيز الخاصة بك.

### مساعدة التكوين (يجب قراءتها)
أسرع وأسهل طريقة للتكوين:
* املأ `transcribe.provider.name` بـ `openai`، بحيث تحتاج فقط إلى ملء كتلة `transcribe.openai` وكتلة تكوين النموذج الكبير `llm` لتتمكن من ترجمة الترجمة. (يمكن ملء `app.proxy` و`model` و`openai.base_url` حسب الحاجة)

طريقة تكوين استخدام نموذج التعرف على الصوت المحلي (توازن بين التكلفة والسرعة والجودة)
* املأ `transcribe.provider.name` بـ `fasterwhisper`، واملأ `transcribe.fasterwhisper.model` بـ `large-v2`، ثم املأ `llm` بتكوين النموذج الكبير، وستقوم النماذج المحلية بالتثبيت والتنزيل تلقائيًا. (مثل `app.proxy` و`openai.base_url` كما هو موضح أعلاه)

تحويل النص إلى صوت (TTS) هو اختياري، منطق التكوين مشابه لما سبق، املأ `tts.provider.name`، ثم املأ الكتل المقابلة تحت `tts`، يمكن ملء رموز الصوت في واجهة المستخدم وفقًا لوثائق المزود المختار (توجد عناوين الوثائق في قسم الأسئلة الشائعة أدناه). قد يتكرر ملء ak وsk من Alibaba Cloud، وذلك لضمان وضوح هيكل التكوين.  
ملاحظة: عند استخدام استنساخ الصوت، يدعم `tts` فقط اختيار `aliyun`.

**للحصول على AccessKey وBucket وAppKey من Alibaba Cloud، يرجى قراءة**：[إرشادات تكوين Alibaba Cloud](./aliyun.md) 

يرجى فهم أن المهمة = التعرف على الصوت + ترجمة النموذج الكبير + خدمة الصوت (TTS وما إلى ذلك، اختيارية)، وهذا سيساعدك في فهم ملف التكوين بشكل أفضل.

## الأسئلة الشائعة

يرجى الانتقال إلى [الأسئلة الشائعة](./faq.md)

## معايير المساهمة
1. لا تقم بتقديم ملفات غير مفيدة، مثل .vscode و.idea، يرجى استخدام .gitignore للتصفية
2. لا تقم بتقديم config.toml، بل استخدم config-example.toml للتقديم

## اتصل بنا
1. انضم إلى مجموعة QQ الخاصة بنا، للإجابة على الأسئلة: 754069680
2. تابع حسابات وسائل التواصل الاجتماعي الخاصة بنا، [Bilibili](https://space.bilibili.com/242124650)، حيث نشارك محتوى عالي الجودة في مجال التكنولوجيا الذكية يوميًا

## تاريخ النجوم

[![Star History Chart](https://api.star-history.com/svg?repos=KrillinAI/KlicStudio&type=Date)](https://star-history.com/#KrillinAI/KlicStudio&Date)