### 1. لا يمكن رؤية ملف إعدادات `app.log`، ولا يمكن معرفة محتوى الخطأ
يرجى من مستخدمي Windows وضع دليل العمل لهذا البرنامج في مجلد غير موجود على القرص C.

### 2. تم إنشاء ملف الإعدادات في النسخة غير المكتبية، ولكن لا يزال يظهر الخطأ "لا يمكن العثور على ملف الإعدادات"
تأكد من أن اسم ملف الإعدادات هو `config.toml`، وليس `config.toml.txt` أو أي شيء آخر. بعد الانتهاء من الإعداد، يجب أن تكون بنية مجلد العمل لهذا البرنامج كما يلي:
```
/── config/
│   └── config.toml
├── cookies.txt （<- ملف cookies.txt اختياري）
└── krillinai.exe
```

### 3. تم ملء إعدادات النموذج الكبير، ولكن يظهر الخطأ "xxxxx يحتاج إلى إعداد xxxxx API Key"
على الرغم من أن خدمات النموذج وخدمات الصوت يمكن أن تستخدم كلاهما خدمات openai، إلا أن هناك أيضًا سيناريوهات حيث يستخدم النموذج الكبير خدمات غير openai بشكل منفصل، لذا فإن هذين الإعدادين منفصلان. بالإضافة إلى إعدادات النموذج الكبير، يرجى البحث عن إعدادات whisper أدناه لملء المفتاح والمعلومات المقابلة.

### 4. يظهر الخطأ "yt-dlp error"
مشكلة في برنامج تنزيل الفيديو، والتي تبدو حاليًا مجرد مشكلة في الشبكة أو إصدار برنامج التنزيل. تحقق مما إذا كان وكيل الشبكة مفتوحًا ومكونًا في إعدادات ملف الإعدادات، كما يُنصح باختيار نقطة اتصال في هونغ كونغ. يتم تثبيت برنامج التنزيل تلقائيًا بواسطة هذا البرنامج، وسأقوم بتحديث مصدر التثبيت، ولكن نظرًا لأنه ليس مصدرًا رسميًا، فقد يكون هناك تأخير. إذا واجهت مشكلة، حاول تحديثه يدويًا، وطريقة التحديث هي:

افتح الطرفية في موقع دليل bin للبرنامج، ثم نفذ
```
./yt-dlp.exe -U
```
استبدل هنا `yt-dlp.exe` باسم برنامج ytdlp الفعلي في نظامك.

### 5. بعد النشر، يتم إنشاء الترجمة بشكل طبيعي، ولكن الترجمة المدمجة في الفيديو تحتوي على الكثير من الرموز غير المفهومة
معظمها بسبب نقص خطوط اللغة الصينية في Linux. يرجى تنزيل خط [微软雅黑](https://modelscope.cn/models/Maranello/KrillinAI_dependency_cn/resolve/master/%E5%AD%97%E4%BD%93/msyh.ttc) و[微软雅黑-bold](https://modelscope.cn/models/Maranello/KrillinAI_dependency_cn/resolve/master/%E5%AD%97%E4%BD%93/msyhbd.ttc) (أو اختيار خط يلبي متطلباتك)، ثم اتبع الخطوات التالية:
1. أنشئ مجلد msyh في /usr/share/fonts/ وانسخ الخطوط التي تم تنزيلها إلى هذا الدليل
2. 
    ```
    cd /usr/share/fonts/msyh
    sudo mkfontscale
    sudo mkfontdir
    fc-cache
    ```

### 6. كيف يمكن ملء رمز الصوت في تحويل النص إلى كلام؟
يرجى الرجوع إلى وثائق مزود خدمة الصوت، وفيما يلي ما يتعلق بهذا المشروع:  
[وثائق OpenAI TTS](https://platform.openai.com/docs/guides/text-to-speech/api-reference)، الموجودة في خيارات الصوت  
[وثائق تفاعل الصوت الذكي من علي بابا](https://help.aliyun.com/zh/isi/developer-reference/overview-of-speech-synthesis)، الموجودة في قائمة الصوت - قيمة معلمة voice