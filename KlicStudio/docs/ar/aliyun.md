## المتطلبات الأساسية
يجب أن يكون لديك حساب على [علي بابا كلاود](https://www.aliyun.com) وتم التحقق من هويتك، حيث أن معظم الخدمات تحتوي على حصة مجانية.

## الحصول على `access_key_id` و `access_key_secret` من علي بابا كلاود
1. انتقل إلى [صفحة إدارة AccessKey في علي بابا كلاود](https://ram.console.aliyun.com/profile/access-keys).
2. انقر على إنشاء AccessKey، وإذا لزم الأمر، اختر طريقة الاستخدام، واختر "استخدام في بيئة التطوير المحلية".
![علي بابا كلاود access key](/docs/images/aliyun_accesskey_1.png)
3. احفظه بشكل آمن، من الأفضل نسخه إلى ملف محلي.

## تفعيل خدمة الصوت من علي بابا كلاود
1. انتقل إلى [صفحة إدارة خدمة الصوت من علي بابا كلاود](https://nls-portal.console.aliyun.com/applist)، وعند الدخول لأول مرة، يجب تفعيل الخدمة.
2. انقر على إنشاء مشروع.
![علي بابا كلاود speech](/docs/images/aliyun_speech_1.png)
3. اختر الوظائف وقم بالتفعيل.
![علي بابا كلاود speech](/docs/images/aliyun_speech_2.png)
4. "توليد الصوت النصي المتدفق (نموذج CosyVoice الكبير)" يحتاج إلى الترقية إلى النسخة التجارية، بينما يمكن استخدام الخدمات الأخرى بنسخة التجربة المجانية.
![علي بابا كلاود speech](/docs/images/aliyun_speech_3.png)
5. انسخ مفتاح التطبيق فقط.
![علي بابا كلاود speech](/docs/images/aliyun_speech_4.png)

## تفعيل خدمة OSS من علي بابا كلاود
1. انتقل إلى [وحدة التحكم في خدمة التخزين الكائني من علي بابا كلاود](https://oss.console.aliyun.com/overview)، وعند الدخول لأول مرة، يجب تفعيل الخدمة.
2. اختر قائمة Buckets من الجانب الأيسر، ثم انقر على إنشاء.
![علي بابا كلاود OSS](/docs/images/aliyun_oss_1.png)
3. اختر الإنشاء السريع، املأ اسم Bucket الذي يتوافق مع المتطلبات واختر منطقة **شنغهاي**، ثم أكمل الإنشاء (الاسم المدخل هنا هو قيمة الإعداد `aliyun.oss.bucket`).
![علي بابا كلاود OSS](/docs/images/aliyun_oss_2.png)
4. بعد الانتهاء من الإنشاء، انتقل إلى Bucket.
![علي بابا كلاود OSS](/docs/images/aliyun_oss_3.png)
5. قم بإيقاف تشغيل مفتاح "منع الوصول العام"، واضبط أذونات القراءة والكتابة على "قراءة عامة".
![علي بابا كلاود OSS](/docs/images/aliyun_oss_4.png)
![علي بابا كلاود OSS](/docs/images/aliyun_oss_5.png)